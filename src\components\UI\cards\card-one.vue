<script setup lang="ts">
import { defineProps } from 'vue';
import { motion } from "motion-v"

const props = defineProps({
      title: {
      type: String,
      default: 'Card Title'
    },
    value: {
      type: String,
      default: '$0'
    },
    percentage: {
      type: String,
      default: '+0%'
    },
    percentageColorClass: {
      type: String,
      default: 'text-green-500' // e.g., 'text-red-500' for negative
    },
    iconBgColorClass: {
      type: String,
      default: 'bg-teal-500' // e.g., 'bg-blue-500'
    },
    // SVG path for the icon to allow easy customization
    iconSvgPath: {
      type: String,
      default: 'M20 7H4c-1.11 0-2 .89-2 2v6c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V9c0-1.11-.89-2-2-2zm0 8H4v-2h16v2zm0-4H4V9h16v2zM15 13H9c-.55 0-1-.45-1-1s.45-1 1-1h6c.55 0 1 .45 1 1s-.45 1-1 1z' // A simple wallet icon SVG path
    }
})
</script>

<template>
  <motion.div  :initial="{ scale: 0 }" :animate="{ scale: 1 }" class="bg-white p-6 rounded-2xl shadow-md flex items-center justify-between w-full max-w-sm" >
    <!-- <div class="bg-white p-6 rounded-2xl shadow-md flex items-center justify-between w-full max-w-sm"> -->
      <div class="flex flex-col">
        <!-- Card Title -->
        <span class="text-gray-600 text-sm font-medium mb-1">{{ props.title }}</span>
        <div class="flex items-baseline">
          <!-- Main Value -->
          <span class="text-2xl font-bold text-black mr-2">{{ props.value }}</span>
          <!-- Percentage Change -->
          <span :class="['text-sm font-semibold', props.percentageColorClass]">{{ props.percentage }}</span>
        </div>
      </div>
      <!-- Icon Section -->
      <div :class="['w-12 h-12 rounded-xl flex items-center justify-center', props.iconBgColorClass]">
        <svg class="w-6 h-6 text-maneb-primary" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path :d="props.iconSvgPath"></path>
        </svg>
      </div>
    <!-- </div> -->
  </motion.div>
</template>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}

.bg-red-50 {
  background-color: #fef2f2;
}
</style>
