<template>
  <!-- Flowbite Pro Modal -->
  <div
    v-if="isOpen"
    id="subject-modal"
    tabindex="-1"
    aria-hidden="true"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900 bg-opacity-50"
    @click.self="closeModal"
  >
    <div class="relative w-full max-w-lg max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow-xl border border-gray-200">
        <!-- Modal header -->
        <div class="flex items-start justify-between p-4 border-b rounded-t border-gray-200">
          <h3 class="text-xl font-semibold text-gray-900">
            {{ isEditing ? 'Edit Subject' : 'Add New Subject' }}
          </h3>
          <button
            @click="closeModal"
            type="button"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
          >
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="p-6">
          <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Subject Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject Name</label>
                  <input
                    v-model="formData.name"
                    type="text"
                    required
                    placeholder="Enter subject name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Subject Code -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject Code</label>
                  <input
                    v-model="formData.code"
                    type="text"
                    required
                    placeholder="Enter subject code (e.g., MATH001)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    v-model="formData.description"
                    rows="3"
                    placeholder="Enter subject description (optional)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  ></textarea>
                </div>

                <!-- Active Status -->
                <div class="flex items-center">
                  <input
                    v-model="formData.isActive"
                    type="checkbox"
                    id="isActive"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                  />
                  <label for="isActive" class="ml-2 block text-sm text-gray-700">
                    Subject is active
                  </label>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- Subject Preview -->
                <div v-if="formData.name && formData.code" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Subject Preview</h4>
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                      <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">{{ formData.name }}</div>
                      <div class="text-sm text-gray-600">{{ formData.code }}</div>
                      <div v-if="formData.description" class="text-xs text-gray-500 mt-1">{{ formData.description }}</div>
                    </div>
                    <div class="ml-auto">
                      <span
                        :class="formData.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      >
                        {{ formData.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </div>
                </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
              <button
                @click="closeModal"
                type="button"
                class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200"
                :disabled="isLoading"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="isLoading || !isFormValid"
                class="text-white bg-maneb-primary hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="isLoading" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </span>
                <span v-else>
                  {{ isEditing ? 'Update Subject' : 'Add Subject' }}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, watchEffect } from 'vue'
import { useGradingStore } from '@/store'
import type { SubjectDto, CreateSubjectRequest, UpdateSubjectRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  subject?: SubjectDto | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  subject: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const formData = ref<{
  name: string
  code: string
  description: string
  isActive: boolean
}>({
  name: '',
  code: '',
  description: '',
  isActive: true
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isEditing = computed(() => !!props.subject?.id)

const isFormValid = computed(() => {
  return formData.value.name.trim() && formData.value.code.trim()
})

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    description: '',
    isActive: true
  }
  error.value = null
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    if (isEditing.value && props.subject?.id) {
      // Update existing subject
      const updateData: UpdateSubjectRequest = {
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.updateSubject(props.subject.id, updateData)
      await sweetAlert.toast.success('Subject updated successfully')
    } else {
      // Create new subject
      const createData: CreateSubjectRequest = {
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.createSubject(createData)
      await sweetAlert.toast.success('Subject added successfully')
    }

    emit('success')
  } catch (err: any) {
    error.value = err.message || 'Failed to save subject'
  } finally {
    isLoading.value = false
  }
}

// Watch for subject prop changes
watch(() => props.subject, (newSubject) => {
  if (newSubject) {
    formData.value = {
      name: newSubject.name || '',
      code: newSubject.code || '',
      description: newSubject.description || '',
      isActive: newSubject.isActive ?? true
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Debug modal state
watchEffect(() => {
  console.log('SubjectModal - isOpen:', props.isOpen)
  console.log('SubjectModal - subject:', props.subject)
})

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  console.log('SubjectModal - isOpen changed to:', isOpen)
  if (!isOpen) {
    resetForm()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
