<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="#F5F5F5" stroke-width="0.5"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="white" stroke-width="0.5"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 0H0V20H28V13.3333L0 20L28 6.66667V0Z" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H9.33333L0 20V0Z" fill="#0858B4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.6667 0H28V6.66667L0 20L18.6667 0Z" fill="#ED3535"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28 19.9999V13.3333L0 19.9999H28Z" fill="#08964F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.33333 0H18.6667L0 20L9.33333 0Z" fill="#FFDD67"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="0" x2="0" y2="20" gradientUnits="userSpaceOnUse">
<stop stop-color="#1DBE4F"/>
<stop offset="1" stop-color="#159B3F"/>
</linearGradient>
</defs>
</svg>
