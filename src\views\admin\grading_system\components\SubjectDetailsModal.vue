<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Subject Details: {{ subject?.name }}
                </h3>
                <button
                  @click="closeModal"
                  class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>

              <div v-if="subject" class="space-y-6">
                <!-- Subject Info -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700">Subject Name</label>
                      <p class="mt-1 text-sm text-gray-900">{{ subject.name }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700">Subject Code</label>
                      <p class="mt-1 text-sm text-gray-900">{{ subject.code }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700">Status</label>
                      <span
                        :class="subject.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                        class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      >
                        {{ subject.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </div>
                  <div v-if="subject.description" class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <p class="mt-1 text-sm text-gray-900">{{ subject.description }}</p>
                  </div>
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-maneb-primary">{{ gradeCount }}</div>
                    <div class="text-sm text-gray-600">Total Grades</div>
                  </div>
                  <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-maneb-primary">{{ boundaryCount }}</div>
                    <div class="text-sm text-gray-600">Grade Boundaries</div>
                  </div>
                  <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-maneb-primary">{{ activeYears.length }}</div>
                    <div class="text-sm text-gray-600">Active Years</div>
                  </div>
                </div>

                <!-- Grade Boundaries -->
                <div>
                  <h4 class="text-lg font-medium text-gray-900 mb-4">Grade Boundaries</h4>
                  <div v-if="subjectBoundaries.length === 0" class="text-center py-8 text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p>No grade boundaries configured for this subject</p>
                  </div>
                  <div v-else class="space-y-4">
                    <div v-for="year in activeYears" :key="year" class="border border-gray-200 rounded-lg p-4">
                      <h5 class="font-medium text-gray-900 mb-3">{{ year }} Academic Year</h5>
                      <div class="grid grid-cols-1 md:grid-cols-5 gap-3">
                        <div
                          v-for="boundary in getBoundariesByYear(year)"
                          :key="boundary.id"
                          class="text-center p-3 rounded-lg"
                          :class="getGradeBoundaryClass(boundary.gradeLevel)"
                        >
                          <div class="font-semibold">Grade {{ boundary.gradeLevel }}</div>
                          <div class="text-sm">{{ boundary.minScore }} - {{ boundary.maxScore }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Recent Grades -->
                <div>
                  <h4 class="text-lg font-medium text-gray-900 mb-4">Recent Grades</h4>
                  <div v-if="recentGrades.length === 0" class="text-center py-8 text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p>No grades recorded for this subject</p>
                  </div>
                  <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Range</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="grade in recentGrades" :key="grade.id" class="hover:bg-gray-50">
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ grade.studentId }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ grade.year }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ grade.startScore }} - {{ grade.endScore }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span :class="getGradeBadgeClass(grade.assignedGrade)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                              {{ grade.assignedGrade }}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span :class="grade.failResult ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                              {{ grade.failResult ? 'Fail' : 'Pass' }}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="closeModal"
            type="button"
            class="w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:w-auto sm:text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useGradingStore } from '@/store'
import type { SubjectDto } from '@/interfaces'

// Props
interface Props {
  isOpen: boolean
  subject?: SubjectDto | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  subject: null
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// Store
const gradingStore = useGradingStore()

// Computed
const gradeCount = computed(() => {
  if (!props.subject?.id) return 0
  return gradingStore.grades.filter(grade => grade.subjectId === props.subject.id).length
})

const boundaryCount = computed(() => {
  if (!props.subject?.id) return 0
  return gradingStore.gradeBoundaries.filter(boundary => boundary.subjectId === props.subject.id).length
})

const subjectBoundaries = computed(() => {
  if (!props.subject?.id) return []
  return gradingStore.gradeBoundaries.filter(boundary => boundary.subjectId === props.subject.id)
})

const activeYears = computed(() => {
  const years = [...new Set(subjectBoundaries.value.map(b => b.year))].sort((a, b) => b - a)
  return years
})

const recentGrades = computed(() => {
  if (!props.subject?.id) return []
  return gradingStore.grades
    .filter(grade => grade.subjectId === props.subject.id)
    .sort((a, b) => b.year - a.year)
    .slice(0, 10)
})

// Methods
const getBoundariesByYear = (year: number) => {
  return subjectBoundaries.value
    .filter(boundary => boundary.year === year)
    .sort((a, b) => b.minScore - a.minScore)
}

const getGradeBoundaryClass = (grade: string): string => {
  const classes = {
    'A': 'bg-green-100 text-green-800',
    'B': 'bg-blue-100 text-blue-800',
    'C': 'bg-yellow-100 text-yellow-800',
    'D': 'bg-orange-100 text-orange-800',
    'F': 'bg-red-100 text-red-800'
  }
  return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getGradeBadgeClass = (grade: string): string => {
  const classes = {
    'A': 'bg-green-100 text-green-800',
    'B': 'bg-blue-100 text-blue-800',
    'C': 'bg-yellow-100 text-yellow-800',
    'D': 'bg-orange-100 text-orange-800',
    'F': 'bg-red-100 text-red-800'
  }
  return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const closeModal = () => {
  emit('close')
}
</script>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>
