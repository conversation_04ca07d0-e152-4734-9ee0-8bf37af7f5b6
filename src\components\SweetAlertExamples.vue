<template>
  <div class="p-6 bg-white rounded-lg shadow-md">
    <h2 class="text-xl font-bold mb-4 text-maneb-primary">SweetAlert Examples</h2>
    
    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
      <!-- Success -->
      <button
        @click="showSuccess"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
      >
        Success Alert
      </button>

      <!-- Error -->
      <button
        @click="showError"
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
      >
        Error <PERSON><PERSON>
      </button>

      <!-- Warning -->
      <button
        @click="showWarning"
        class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
      >
        Warning Alert
      </button>

      <!-- Info -->
      <button
        @click="showInfo"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        Info Alert
      </button>

      <!-- Confirm -->
      <button
        @click="showConfirm"
        class="px-4 py-2 bg-maneb-primary text-white rounded hover:bg-maneb-primary-dark transition-colors"
      >
        Confirm Dialog
      </button>

      <!-- Delete Confirm -->
      <button
        @click="showDeleteConfirm"
        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
      >
        Delete Confirm
      </button>

      <!-- Loading -->
      <button
        @click="showLoading"
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
      >
        Loading Alert
      </button>

      <!-- Toast Success -->
      <button
        @click="showToastSuccess"
        class="px-4 py-2 bg-green-400 text-white rounded hover:bg-green-500 transition-colors"
      >
        Toast Success
      </button>

      <!-- Toast Error -->
      <button
        @click="showToastError"
        class="px-4 py-2 bg-red-400 text-white rounded hover:bg-red-500 transition-colors"
      >
        Toast Error
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import sweetAlert from '@/utils/sweetAlert'

const showSuccess = () => {
  sweetAlert.success('Success!', 'Your operation was completed successfully.')
}

const showError = () => {
  sweetAlert.error('Error!', 'Something went wrong. Please try again.')
}

const showWarning = () => {
  sweetAlert.warning('Warning!', 'Please review your input before proceeding.')
}

const showInfo = () => {
  sweetAlert.info('Information', 'Here is some important information for you.')
}

const showConfirm = async () => {
  const result = await sweetAlert.confirm(
    'Are you sure?',
    'Do you want to proceed with this action?'
  )
  
  if (result.isConfirmed) {
    sweetAlert.toast.success('Action confirmed!')
  } else {
    sweetAlert.toast.info('Action cancelled')
  }
}

const showDeleteConfirm = async () => {
  const result = await sweetAlert.confirmDelete(
    'Delete User?',
    'This will permanently delete the user account.'
  )
  
  if (result.isConfirmed) {
    sweetAlert.toast.success('User deleted successfully!')
  }
}

const showLoading = () => {
  sweetAlert.loading('Processing...', 'Please wait while we process your request')
  
  // Simulate async operation
  setTimeout(() => {
    sweetAlert.close()
    sweetAlert.toast.success('Operation completed!')
  }, 3000)
}

const showToastSuccess = () => {
  sweetAlert.toast.success('Toast notification!')
}

const showToastError = () => {
  sweetAlert.toast.error('Something went wrong!')
}
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}
</style>
