@import 'tailwindcss';
@import 'flowbite/src/themes/default';
@plugin "flowbite/plugin";

/* MANEB Official Color Scheme */
:root {
  --maneb-red: #a12c2c;
  --maneb-red-dark: #8b2424;
  --maneb-red-light: #b83434;
  --maneb-gold: #D97706;
  --maneb-gold-light: #F59E0B;
  --maneb-black: #1F2937;
  --maneb-white: #FFFFFF;
}

/* Global MANEB color utilities */
.bg-maneb-primary {
  background-color: var(--maneb-red);
}

.bg-maneb-primary-dark {
  background-color: var(--maneb-red-dark);
}

.bg-maneb-secondary {
  background-color: var(--maneb-gold);
}

.text-maneb-primary {
  color: var(--maneb-red);
}

.text-maneb-secondary {
  color: var(--maneb-gold);
}

.border-maneb-primary {
  border-color: var(--maneb-red);
}

.border-maneb-secondary {
  border-color: var(--maneb-gold);
}

/* Override Flowbite primary colors with MANEB colors */
.bg-primary-600 {
  background-color: var(--maneb-red) !important;
}

.bg-primary-700 {
  background-color: var(--maneb-red-dark) !important;
}

.text-primary-600 {
  color: var(--maneb-red) !important;
}

.text-primary-500 {
  color: var(--maneb-gold) !important;
}

.border-primary-600 {
  border-color: var(--maneb-red) !important;
}

.focus\:ring-primary-300:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.3) !important;
}

.focus\:border-primary-600:focus {
  border-color: var(--maneb-red) !important;
}

.hover\:bg-primary-700:hover {
  background-color: var(--maneb-red-dark) !important;
}
