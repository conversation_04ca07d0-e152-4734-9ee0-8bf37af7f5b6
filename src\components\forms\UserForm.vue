<template>
  <div class="p-6 space-y-6">
    <!-- Form Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">
        {{ isEditing ? 'Edit Student/Faculty' : 'Register New Student/Faculty' }}
      </h3>
      <button 
        @click="$emit('close')"
        type="button" 
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
      >
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </div>

    <!-- Error Alert -->
    <div v-if="error" class="flex items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800" role="alert">
      <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
      </svg>
      <span class="sr-only">Error</span>
      <div>{{ error }}</div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Personal Information Section -->
      <div>
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Personal Information</h4>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <!-- First Name -->
          <div>
            <label for="firstName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              First Name <span class="text-red-500">*</span>
            </label>
            <input 
              type="text" 
              id="firstName" 
              v-model="formData.firstName"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
              placeholder="John" 
              required
              :disabled="isLoading"
            >
          </div>

          <!-- Last Name -->
          <div>
            <label for="lastName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Last Name <span class="text-red-500">*</span>
            </label>
            <input 
              type="text" 
              id="lastName" 
              v-model="formData.lastName"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
              placeholder="Doe" 
              required
              :disabled="isLoading"
            >
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Email Address
            </label>
            <input 
              type="email" 
              id="email" 
              v-model="formData.email"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
              placeholder="<EMAIL>"
              :disabled="isLoading"
            >
          </div>

          <!-- Username -->
          <div>
            <label for="userName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Username
            </label>
            <input 
              type="text" 
              id="userName" 
              v-model="formData.userName"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
              placeholder="johndoe"
              :disabled="isLoading"
            >
          </div>

          <!-- Gender -->
          <div>
            <label for="gender" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Gender
            </label>
            <select 
              id="gender" 
              v-model="formData.gender"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              :disabled="isLoading"
            >
              <option value="">Select Gender</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <!-- Date of Birth -->
          <div>
            <label for="dateOfBirth" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Date of Birth
            </label>
            <input
              type="date"
              id="dateOfBirth"
              v-model="formData.dateOfBirth"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              :disabled="isLoading"
            >
          </div>
        </div>
      </div>

      <!-- Identification Section -->
      <div>
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Identification</h4>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <!-- ID Type -->
          <div>
            <label for="idType" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              ID Type
            </label>
            <select 
              id="idType" 
              v-model="formData.idType"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              :disabled="isLoading"
            >
              <option value="">Select ID Type</option>
              <option value="National ID">National ID</option>
              <option value="Passport">Passport</option>
              <option value="Driver's License">Driver's License</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <!-- ID Number -->
          <div>
            <label for="idNumber" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              ID Number
            </label>
            <input 
              type="text" 
              id="idNumber" 
              v-model="formData.idNumber"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
              placeholder="Enter ID number"
              :disabled="isLoading"
            >
          </div>
        </div>
      </div>

      <!-- Status Section (only for editing) -->
      <div v-if="isEditing">
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Status</h4>
        <div>
          <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
            User Status
          </label>
          <select 
            id="status" 
            v-model="formData.status"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            :disabled="isLoading"
          >
            <option value="Unapproved">Unapproved</option>
            <option value="Approved">Approved</option>
            <option value="SecondApproved">Second Approved</option>
            <option value="Rejected">Rejected</option>
          </select>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button 
          @click="$emit('close')"
          type="button" 
          class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button 
          type="submit" 
          :disabled="isLoading"
          class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isEditing ? 'Updating...' : 'Registering...' }}
          </span>
          <span v-else>
            {{ isEditing ? 'Update Profile' : 'Register Student/Faculty' }}
          </span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/store/user.store'
import type { UserDto, CreateUserRequest, UpdateUserRequest, Gender, RecordStatus } from '@/interfaces'

// Props
interface Props {
  user?: UserDto | null
  isOpen: boolean
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
  isOpen: false
})

// Emits
const emit = defineEmits<{
  close: []
  success: [user: UserDto]
}>()

// Store
const userStore = useUserStore()

// Reactive state (using only actual API fields)
const formData = ref<CreateUserRequest & {
  status?: RecordStatus;
  dateOfBirth?: string;
}>({
  firstName: '',
  lastName: '',
  email: '',
  userName: '',
  gender: undefined,
  dateOfBirth: undefined,
  idType: '',
  idNumber: '',
  status: 'Unapproved'
})

const error = ref<string | null>(null)

// Computed properties
const isEditing = computed(() => !!props.user?.id)
const isLoading = computed(() => userStore.isLoading)

// Methods
const resetForm = () => {
  formData.value = {
    firstName: '',
    lastName: '',
    email: '',
    userName: '',
    gender: undefined,
    dateOfBirth: undefined,
    idType: '',
    idNumber: '',
    status: 'Unapproved'
  }
  error.value = null
}

const populateForm = (user: UserDto) => {
  formData.value = {
    firstName: user.firstName || '',
    lastName: user.lastName || '',
    email: user.email || '',
    userName: user.userName || '',
    gender: user.gender,
    dateOfBirth: user.dateOfBirth ? new Date(user.dateOfBirth).toISOString().split('T')[0] : undefined,
    idType: user.idType || '',
    idNumber: user.idNumber || '',
    status: user.status || 'Unapproved'
  }
}

const handleSubmit = async () => {
  try {
    error.value = null

    // Prepare data for submission (using only API fields)
    const submitData: any = {
      ...formData.value,
      dateOfBirth: formData.value.dateOfBirth ? new Date(formData.value.dateOfBirth) : undefined
    }

    // Remove the status field for creation (it's set by the backend)
    if (!isEditing.value) {
      delete submitData.status
    }

    let result: UserDto

    if (isEditing.value && props.user?.id) {
      // Update existing user
      await userStore.updateUser(props.user.id, submitData as UpdateUserRequest)
      result = { ...props.user, ...submitData } as UserDto
    } else {
      // Create new user
      result = await userStore.createUser(submitData as CreateUserRequest)
    }

    emit('success', result)
    emit('close')
    resetForm()

  } catch (err: any) {
    error.value = err.message || `Failed to ${isEditing.value ? 'update' : 'register'} student/faculty`
  }
}

// Watchers
watch(() => props.user, (newUser) => {
  if (newUser) {
    populateForm(newUser)
  } else {
    resetForm()
  }
}, { immediate: true })

watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    error.value = null
  }
})
</script>
