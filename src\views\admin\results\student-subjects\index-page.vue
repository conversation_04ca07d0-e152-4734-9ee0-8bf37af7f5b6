<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button
              @click="goBack"
              class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Back to Results
            </button>
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">Student Subject Details</h1>
              <p class="text-sm text-gray-600 mt-1">
                {{ studentId }} - {{ year }} Examination Session
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="printTranscript"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Print Transcript
            </button>
            <button
              @click="printCertificate"
              class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Print Certificate
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Student Summary Card -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="w-16 h-16 bg-red-50 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-600">Student ID</p>
            <p class="text-lg font-semibold text-gray-900">{{ studentId }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-red-50 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-600">Average Score</p>
            <p class="text-lg font-semibold text-gray-900">{{ studentData?.averageScore.toFixed(1) }}%</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-red-50 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-600">Average Grade</p>
            <p class="text-lg font-semibold text-gray-900">{{ studentData?.averageGrade }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-red-50 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg class="w-8 h-8 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-600">Pass Rate</p>
            <p class="text-lg font-semibold text-gray-900">{{ studentData?.passRate.toFixed(1) }}%</p>
          </div>
        </div>
      </div>

      <!-- Subjects List -->
      <div class="space-y-6">
        <div
          v-for="subject in studentData?.subjects"
          :key="subject.subjectId"
          class="bg-white rounded-lg shadow-sm border border-gray-200"
        >
          <!-- Subject Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ subject.subjectName }}</h3>
                <p class="text-sm text-gray-600">{{ subject.subjectCode }}</p>
              </div>
              <div class="flex items-center space-x-4">
                <div class="text-right">
                  <p class="text-sm font-medium text-gray-600">Average Score</p>
                  <p class="text-lg font-semibold text-gray-900">{{ subject.averageScore.toFixed(1) }}%</p>
                </div>
                <span :class="resultsStore.getGradeBadgeClass(subject.overallGrade)" class="inline-flex px-3 py-1 text-sm font-semibold rounded-full">
                  Grade {{ subject.overallGrade }}
                </span>
                <span :class="subject.passStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex px-3 py-1 text-sm font-semibold rounded-full">
                  {{ subject.passStatus ? 'PASS' : 'FAIL' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Papers Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paper</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="paper in subject.papers" :key="paper.paperId" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ paper.paperName }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ paper.paperCode }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="font-medium">{{ paper.score }}%</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="resultsStore.getGradeBadgeClass(paper.grade)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ paper.grade }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="paper.passStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ paper.passStatus ? 'Pass' : 'Fail' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      @click="viewPaperDetails(paper)"
                      class="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
                    >
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                      View Details
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading student data...
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="!studentData" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="text-lg font-medium text-gray-900 mb-2">No data found</p>
        <p class="text-gray-600">No examination data found for this student and year.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useResultsStore } from '@/store/results.store'
import { useGradingStore } from '@/store/grading.store'
import type { ExaminationResultDto, StudentPaperResult } from '@/interfaces'
import { sweetAlert } from '@/utils/sweetAlert'

// Router and stores
const route = useRoute()
const router = useRouter()
const resultsStore = useResultsStore()
const gradingStore = useGradingStore()

// Route params
const studentId = computed(() => route.params.studentId as string)
const year = computed(() => parseInt(route.params.year as string))

// State
const isLoading = ref(false)

// Computed
const studentData = computed(() => {
  return resultsStore.examinationResults.find(
    result => result.studentId === studentId.value && result.year === year.value
  )
})

// Methods
const goBack = () => {
  router.push({ name: 'admin.results' })
}

const printTranscript = async () => {
  if (!studentData.value) return

  try {
    await sweetAlert.info(
      'Generating Transcript',
      `Generating academic transcript for ${studentId.value} for ${year.value} examination...`,
      'info'
    )

    const result = studentData.value
    // Generate comprehensive transcript for the student's entire exam year
    const transcriptWindow = window.open('', '_blank', 'width=800,height=600')
    if (transcriptWindow) {
      transcriptWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MANEB Academic Transcript - ${result.studentId}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
            .transcript { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; border-bottom: 3px solid #a12c2c; padding: 20px; margin-bottom: 30px; }
            .logo { width: 80px; height: 80px; margin: 0 auto 15px; }
            .student-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
            .subject-section { margin-bottom: 30px; }
            .subject-header { background: #a12c2c; color: white; padding: 15px; border-radius: 8px 8px 0 0; }
            .papers-table { width: 100%; border-collapse: collapse; border: 1px solid #ddd; }
            .papers-table th, .papers-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            .papers-table th { background: #f8f9fa; font-weight: 600; }
            .grade-a { background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 4px; }
            .grade-b { background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; }
            .grade-c { background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; }
            .grade-d { background: #fed7aa; color: #c2410c; padding: 4px 8px; border-radius: 4px; }
            .grade-f { background: #fecaca; color: #dc2626; padding: 4px 8px; border-radius: 4px; }
            .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 30px; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; }
            @media print { body { margin: 0; } .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="transcript">
            <div class="header">
              <h1>MALAWI NATIONAL EXAMINATIONS BOARD</h1>
              <h2>ACADEMIC TRANSCRIPT</h2>
              <p>${result.level} Examination - ${result.year}</p>
            </div>

            <div class="student-info">
              <h3>Student Information</h3>
              <div class="info-grid">
                <div><strong>Student ID:</strong> ${result.studentId}</div>
                <div><strong>Exam Number:</strong> ${result.examNumber || 'N/A'}</div>
                <div><strong>Examination Level:</strong> ${result.level}</div>
                <div><strong>Examination Year:</strong> ${result.year}</div>
                <div><strong>Total Subjects:</strong> ${result.totalSubjects}</div>
                <div><strong>Overall Average:</strong> ${result.averageScore.toFixed(1)}% (Grade ${result.averageGrade})</div>
              </div>
            </div>

            ${result.subjects.map(subject => `
              <div class="subject-section">
                <div class="subject-header">
                  <h3>${subject.subjectName} (${subject.subjectCode})</h3>
                  <p>Subject Average: ${subject.averageScore.toFixed(1)}% | Grade: ${subject.overallGrade} | Status: ${subject.passStatus ? 'PASS' : 'FAIL'}</p>
                </div>
                <table class="papers-table">
                  <thead>
                    <tr>
                      <th>Paper</th>
                      <th>Code</th>
                      <th>Score</th>
                      <th>Grade</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${subject.papers.map(paper => `
                      <tr>
                        <td>${paper.paperName}</td>
                        <td>${paper.paperCode}</td>
                        <td>${paper.score}%</td>
                        <td><span class="grade-${paper.grade.toLowerCase()}">${paper.grade}</span></td>
                        <td>${paper.passStatus ? 'Pass' : 'Fail'}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            `).join('')}

            <div class="summary">
              <h3>Overall Performance Summary</h3>
              <div class="info-grid">
                <div><strong>Total Subjects Taken:</strong> ${result.totalSubjects}</div>
                <div><strong>Subjects Passed:</strong> ${result.passedSubjects}</div>
                <div><strong>Subjects Failed:</strong> ${result.failedSubjects}</div>
                <div><strong>Pass Rate:</strong> ${result.passRate.toFixed(1)}%</div>
                <div><strong>Overall Average Score:</strong> ${result.averageScore.toFixed(1)}%</div>
                <div><strong>Overall Grade:</strong> ${result.averageGrade}</div>
              </div>
            </div>

            <div class="footer">
              <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Malawi National Examinations Board</strong></p>
              <p>This is an official academic transcript</p>
            </div>
          </div>
          <scr` + `ipt>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </scr` + `ipt>
        </body>
        </html>
      `)
      transcriptWindow.document.close()
    } else {
      await sweetAlert.error('Error', 'Unable to open transcript window. Please check your popup blocker settings.')
    }

  } catch (error: any) {
    await sweetAlert.error('Error', error.message || 'Failed to generate transcript')
  }
}

const printCertificate = async () => {
  if (!studentData.value) return

  try {
    await sweetAlert.info(
      'Generating Certificate',
      `Generating certificate for ${studentId.value} for ${year.value} examination...`,
      'info'
    )

    const result = studentData.value
    // Generate comprehensive certificate for the student's entire exam year
    const certificate = {
      studentId: result.studentId,
      examNumber: result.examNumber,
      year: result.year,
      examSession: `${result.year} ${result.level} Main Session`,
      subjects: result.subjects,
      overallPerformance: {
        totalSubjects: result.totalSubjects,
        passedSubjects: result.passedSubjects,
        failedSubjects: result.failedSubjects,
        overallGrade: result.averageGrade,
        passRate: result.passRate
      },
      certificateNumber: `MANEB/${result.level}/${result.year}/${result.studentId}`,
      issueDate: new Date(),
      issuedBy: 'Malawi National Examinations Board'
    }

    // Open certificate in new window for printing
    const certificateWindow = window.open('', '_blank', 'width=800,height=600')
    if (certificateWindow) {
      certificateWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MANEB Certificate - ${certificate.studentId}</title>
          <style>
            body { font-family: 'Times New Roman', serif; margin: 0; padding: 40px; background: #f8f9fa; }
            .certificate { max-width: 800px; margin: 0 auto; background: white; padding: 60px; border: 8px solid #a12c2c; border-radius: 15px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 40px; }
            .logo { width: 100px; height: 100px; margin: 0 auto 20px; background: #a12c2c; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; }
            .title { font-size: 32px; font-weight: bold; color: #a12c2c; margin: 20px 0; text-transform: uppercase; letter-spacing: 2px; }
            .subtitle { font-size: 18px; color: #666; margin-bottom: 30px; }
            .certificate-body { text-align: center; margin: 40px 0; }
            .student-name { font-size: 28px; font-weight: bold; color: #333; margin: 20px 0; text-decoration: underline; }
            .achievement { font-size: 16px; line-height: 1.8; color: #555; margin: 30px 0; }
            .subjects-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 30px 0; text-align: left; }
            .subject-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #a12c2c; }
            .subject-name { font-weight: bold; color: #333; margin-bottom: 5px; }
            .subject-grade { color: #a12c2c; font-size: 18px; font-weight: bold; }
            .performance-summary { background: #a12c2c; color: white; padding: 20px; border-radius: 8px; margin: 30px 0; }
            .footer { display: flex; justify-content: space-between; align-items: center; margin-top: 60px; padding-top: 30px; border-top: 2px solid #a12c2c; }
            .signature-section { text-align: center; }
            .signature-line { border-bottom: 2px solid #333; width: 200px; margin: 30px auto 10px; }
            .date { font-style: italic; color: #666; }
            .certificate-number { position: absolute; top: 20px; right: 20px; font-size: 12px; color: #666; }
            @media print { body { background: white; } .certificate { border: 3px solid #a12c2c; box-shadow: none; } }
          </style>
        </head>
        <body>
          <div class="certificate-number">Certificate No: ${certificate.certificateNumber}</div>
          <div class="certificate">
            <div class="header">
              <div class="logo">MANEB</div>
              <h1 class="title">Malawi National Examinations Board</h1>
              <h2 class="subtitle">Certificate of Examination</h2>
              <p class="date">${certificate.examSession}</p>
            </div>

            <div class="certificate-body">
              <p class="achievement">This is to certify that</p>
              <div class="student-name">${certificate.studentId}</div>
              <p class="achievement">
                has successfully completed the ${result.level} examination in ${certificate.year}
                and has been awarded the following grades:
              </p>

              <div class="subjects-grid">
                ${certificate.subjects.map(subject => `
                  <div class="subject-item">
                    <div class="subject-name">${subject.subjectName}</div>
                    <div class="subject-grade">Grade ${subject.overallGrade}</div>
                    <div style="font-size: 12px; color: #666;">${subject.passStatus ? 'PASS' : 'FAIL'}</div>
                  </div>
                `).join('')}
              </div>

              <div class="performance-summary">
                <h3 style="margin-top: 0;">Overall Performance</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; text-align: center;">
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.totalSubjects}</div>
                    <div>Total Subjects</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.passedSubjects}</div>
                    <div>Subjects Passed</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.overallGrade}</div>
                    <div>Overall Grade</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.passRate.toFixed(1)}%</div>
                    <div>Pass Rate</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="footer">
              <div class="signature-section">
                <div class="signature-line"></div>
                <p><strong>Director of Examinations</strong></p>
                <p>Malawi National Examinations Board</p>
              </div>
              <div class="signature-section">
                <div class="signature-line"></div>
                <p><strong>Date of Issue</strong></p>
                <p>${new Date(certificate.issueDate).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
          <scr` + `ipt>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </scr` + `ipt>
        </body>
        </html>
      `)
      certificateWindow.document.close()
    } else {
      await sweetAlert.error('Error', 'Unable to open certificate window. Please check your popup blocker settings.')
    }

  } catch (error: any) {
    await sweetAlert.error('Error', error.message || 'Failed to generate certificate')
  }
}

const viewPaperDetails = (paper: StudentPaperResult) => {
  // TODO: Implement paper details view or modal
  sweetAlert.info('Paper Details', `Viewing details for ${paper.paperName}`)
}

// Lifecycle
onMounted(async () => {
  if (resultsStore.examinationResults.length === 0) {
    isLoading.value = true
    try {
      // Initialize grading store data first
      await gradingStore.initializeStore()
      
      // Then generate results
      await resultsStore.generateResults(
        gradingStore.gradeBoundaries,
        gradingStore.subjects,
        gradingStore.papers
      )
    } catch (error) {
      console.error('Failed to load results data:', error)
    } finally {
      isLoading.value = false
    }
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>
