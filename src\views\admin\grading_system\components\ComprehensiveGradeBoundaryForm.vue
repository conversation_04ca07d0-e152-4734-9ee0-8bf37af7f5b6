<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" v-if="isOpen">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          Setup Comprehensive Grade Boundaries
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form Content -->
      <div class="mt-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Exam Type -->
            <div>
              <label for="examType" class="block text-sm font-medium text-gray-700 mb-2">
                Exam Type <span class="text-red-500">*</span>
              </label>
              <select
                id="examType"
                v-model="formData.examType"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Exam Type</option>
                <option v-for="examType in examTypes" :key="examType.value" :value="examType.value">
                  {{ examType.label }} - {{ examType.description }}
                </option>
              </select>
            </div>

            <!-- Exam Year -->
            <div>
              <label for="year" class="block text-sm font-medium text-gray-700 mb-2">
                Exam Year <span class="text-red-500">*</span>
              </label>
              <select
                id="year"
                v-model="formData.year"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Year</option>
                <option v-for="year in availableYears" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>

            <!-- Subject -->
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                Subject <span class="text-red-500">*</span>
              </label>
              <select
                id="subject"
                v-model="formData.subjectId"
                @change="onSubjectChange"
                :disabled="isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Subject</option>
                <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }} ({{ subject.code }})
                </option>
              </select>
            </div>

            <!-- Paper -->
            <div>
              <label for="paper" class="block text-sm font-medium text-gray-700 mb-2">
                Paper <span class="text-red-500">*</span>
              </label>
              <select
                id="paper"
                v-model="formData.paperId"
                :disabled="!formData.subjectId || isLoading"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
                required
              >
                <option value="">Select Paper</option>
                <option v-for="paper in availablePapers" :key="paper.id" :value="paper.id">
                  {{ paper.name }} ({{ paper.code }})
                </option>
              </select>
            </div>
          </div>

          <!-- Grade Boundaries Section -->
          <div class="border-t border-gray-200 pt-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Grade Boundaries</h4>
            <p class="text-sm text-gray-600 mb-6">
              Set the score ranges for each grade level. Ensure ranges don't overlap and cover the full 0-100 range.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
              <!-- Grade A -->
              <div class="border border-green-200 bg-green-50 rounded-lg p-4">
                <h5 class="font-semibold text-green-800 mb-3">Grade A</h5>
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-green-700 mb-1">Min Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.A.minScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-green-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-green-700 mb-1">Max Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.A.maxScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-green-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-green-700 mb-1">Description</label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries.A.description"
                      placeholder="e.g., Distinction"
                      class="w-full text-sm border border-green-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                    />
                  </div>
                </div>
              </div>

              <!-- Grade B -->
              <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                <h5 class="font-semibold text-blue-800 mb-3">Grade B</h5>
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-blue-700 mb-1">Min Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.B.minScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-blue-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-blue-700 mb-1">Max Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.B.maxScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-blue-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-blue-700 mb-1">Description</label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries.B.description"
                      placeholder="e.g., Credit"
                      class="w-full text-sm border border-blue-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                    />
                  </div>
                </div>
              </div>

              <!-- Grade C -->
              <div class="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                <h5 class="font-semibold text-yellow-800 mb-3">Grade C</h5>
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-yellow-700 mb-1">Min Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.C.minScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-yellow-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-yellow-700 mb-1">Max Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.C.maxScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-yellow-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-yellow-700 mb-1">Description</label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries.C.description"
                      placeholder="e.g., Pass"
                      class="w-full text-sm border border-yellow-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                    />
                  </div>
                </div>
              </div>

              <!-- Grade D -->
              <div class="border border-orange-200 bg-orange-50 rounded-lg p-4">
                <h5 class="font-semibold text-orange-800 mb-3">Grade D</h5>
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-orange-700 mb-1">Min Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.D.minScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-orange-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-orange-700 mb-1">Max Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.D.maxScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-orange-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-orange-700 mb-1">Description</label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries.D.description"
                      placeholder="e.g., Pass"
                      class="w-full text-sm border border-orange-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                    />
                  </div>
                </div>
              </div>

              <!-- Grade F -->
              <div class="border border-red-200 bg-red-50 rounded-lg p-4">
                <h5 class="font-semibold text-red-800 mb-3">Grade F</h5>
                <div class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-red-700 mb-1">Min Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.F.minScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-red-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-red-700 mb-1">Max Score</label>
                    <input
                      type="number"
                      v-model.number="formData.gradeBoundaries.F.maxScore"
                      min="0"
                      max="100"
                      class="w-full text-sm border border-red-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                      required
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-red-700 mb-1">Description</label>
                    <input
                      type="text"
                      v-model="formData.gradeBoundaries.F.description"
                      placeholder="e.g., Fail"
                      class="w-full text-sm border border-red-300 rounded-md px-2 py-1 focus:ring-maneb-primary focus:border-maneb-primary"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Validation Errors -->
          <div v-if="validationErrors.length > 0" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Validation Errors</h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <li v-for="error in validationErrors" :key="error">{{ error }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="closeModal"
              :disabled="isLoading"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="!isFormValid || isLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-maneb-primary border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </span>
              <span v-else>Create Grade Boundaries</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import { EXAM_TYPES } from '@/services/grading.service'
import type { ExamType, ComprehensiveGradeBoundaryRequest, GradeBoundarySet } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const isLoading = ref(false)
const validationErrors = ref<string[]>([])

const formData = ref<{
  examType: ExamType | ''
  subjectId: string
  paperId: string
  year: number | ''
  gradeBoundaries: {
    A: GradeBoundarySet
    B: GradeBoundarySet
    C: GradeBoundarySet
    D: GradeBoundarySet
    F: GradeBoundarySet
  }
}>({
  examType: '',
  subjectId: '',
  paperId: '',
  year: '',
  gradeBoundaries: {
    A: { gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction' },
    B: { gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit' },
    C: { gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass' },
    D: { gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass' },
    F: { gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail' }
  }
})

// Computed
const examTypes = computed(() => EXAM_TYPES)

const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    years.push(i)
  }
  return years.sort((a, b) => b - a)
})

const availablePapers = computed(() => {
  if (!formData.value.subjectId) return []
  return gradingStore.papers.filter(paper =>
    paper.subjectId === formData.value.subjectId && paper.isActive && !paper.isDeleted
  )
})

const isFormValid = computed(() => {
  return formData.value.examType &&
         formData.value.subjectId &&
         formData.value.paperId &&
         formData.value.year &&
         validationErrors.value.length === 0
})

// Methods
const resetForm = () => {
  formData.value = {
    examType: '',
    subjectId: '',
    paperId: '',
    year: '',
    gradeBoundaries: {
      A: { gradeLevel: 'A', minScore: 80, maxScore: 100, description: 'Distinction' },
      B: { gradeLevel: 'B', minScore: 70, maxScore: 79, description: 'Credit' },
      C: { gradeLevel: 'C', minScore: 60, maxScore: 69, description: 'Pass' },
      D: { gradeLevel: 'D', minScore: 50, maxScore: 59, description: 'Pass' },
      F: { gradeLevel: 'F', minScore: 0, maxScore: 49, description: 'Fail' }
    }
  }
  validationErrors.value = []
}

const onSubjectChange = () => {
  formData.value.paperId = ''
}

const validateGradeBoundaries = (): string[] => {
  const errors: string[] = []
  const boundaries = Object.values(formData.value.gradeBoundaries)

  // Check for overlapping ranges
  const sortedBoundaries = [...boundaries].sort((a, b) => b.minScore - a.minScore)
  for (let i = 0; i < sortedBoundaries.length - 1; i++) {
    const current = sortedBoundaries[i]
    const next = sortedBoundaries[i + 1]
    if (current.minScore <= next.maxScore) {
      errors.push(`Grade boundaries overlap between ${current.gradeLevel} and ${next.gradeLevel}`)
    }
  }

  // Check for gaps in coverage
  const allScores = new Set<number>()
  boundaries.forEach(boundary => {
    for (let score = boundary.minScore; score <= boundary.maxScore; score++) {
      allScores.add(score)
    }
  })

  if (allScores.size !== 101) { // Should cover 0-100 (101 values)
    errors.push('Grade boundaries must cover the complete range from 0 to 100 without gaps')
  }

  // Check individual boundary validity
  boundaries.forEach(boundary => {
    if (boundary.minScore < 0 || boundary.maxScore > 100) {
      errors.push(`Grade ${boundary.gradeLevel}: Scores must be between 0 and 100`)
    }
    if (boundary.minScore > boundary.maxScore) {
      errors.push(`Grade ${boundary.gradeLevel}: Minimum score cannot be greater than maximum score`)
    }
  })

  return errors
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  // Validate grade boundaries
  const errors = validateGradeBoundaries()
  if (errors.length > 0) {
    validationErrors.value = errors
    return
  }

  isLoading.value = true
  validationErrors.value = []

  try {
    const request: ComprehensiveGradeBoundaryRequest = {
      examType: formData.value.examType as ExamType,
      subjectId: formData.value.subjectId,
      paperId: formData.value.paperId,
      year: formData.value.year as number,
      gradeBoundaries: Object.values(formData.value.gradeBoundaries),
      isActive: true
    }

    const response = await gradingStore.createComprehensiveGradeBoundaries(request)

    if (response.success) {
      await sweetAlert.toast.success(`Successfully created ${response.createdBoundaries.length} grade boundaries`)
      emit('success')
      closeModal()
    } else {
      validationErrors.value = response.errors || ['Failed to create grade boundaries']
    }
  } catch (err: any) {
    validationErrors.value = [err.message || 'Failed to create grade boundaries']
  } finally {
    isLoading.value = false
  }
}

// Watch for form changes to clear validation errors
watch(() => formData.value.gradeBoundaries, () => {
  if (validationErrors.value.length > 0) {
    validationErrors.value = []
  }
}, { deep: true })

// Reset form when modal opens
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    resetForm()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
