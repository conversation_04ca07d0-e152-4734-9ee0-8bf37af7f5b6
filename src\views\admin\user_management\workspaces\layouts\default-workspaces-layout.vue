<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'

const workspaceBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Workspaces', href: '/admin/user-management/workspaces', current: true },
];
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces/table';

const workspaceColumns: TableColumn[] = [
  { key: 'workspace_id', label: 'Workspace ID', sortable: true },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'owner', label: 'Owner', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Created At', sortable: true },
];

const workspaceData: TableRowData[] = [
  { workspace_id: 'WS001', name: 'Development Team', owner: '<PERSON>', status: 'Active', created_at: '2023-01-15' },
  { workspace_id: 'WS002', name: 'Marketing Campaign', owner: 'Bob Johnson', status: 'Inactive', created_at: '2023-02-20' },
  { workspace_id: 'WS003', name: 'Product Design', owner: 'Charlie Brown', status: 'Active', created_at: '2023-03-10' },
  { workspace_id: 'WS004', name: 'Sales Pipeline', owner: 'Diana Prince', status: 'Active', created_at: '2023-04-01' },
  { workspace_id: 'WS005', name: 'Customer Support', owner: 'Eve Adams', status: 'Inactive', created_at: '2023-05-05' },
];

const workspaceFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const workspaceRadioFilters = [
  { label: 'All Workspaces', value: 'All' },
  { label: 'Active Workspaces', value: 'Active' },
  { label: 'Inactive Workspaces', value: 'Inactive' },
];

</script>

<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <BreadcrumbsActions :breadcrumbs="workspaceBreadcrumbs" />
        <DefaultTable
          :columns="workspaceColumns"
          :data="workspaceData"
          title="Workspaces"
          :filterOptions="workspaceFilterOptions"
          :radioFilters="workspaceRadioFilters"
          addButtonLabel="Add New Workspace"
          :showAddButton="true"
        />
        <Footer />
      </div>
</template>
