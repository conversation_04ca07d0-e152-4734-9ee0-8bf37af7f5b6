<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <a href="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
            Admin
          </a>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/admin/user-management" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">User Management</a>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Roles</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-black">Roles Management</h3>
          <button
            @click="showCreateModal = true"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add New Role
          </button>
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role Name
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Permissions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created Date
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading">
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex justify-center items-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-maneb-primary"></div>
                  <span class="ml-2 text-gray-500">Loading roles...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="sampleRoles.length === 0">
              <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                No roles found
              </td>
            </tr>
            <tr v-else v-for="role in sampleRoles" :key="role.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-black">{{ role.name }}</div>
                <div class="text-sm text-gray-500">ID: {{ role.id }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(role.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ role.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                  <svg class="h-4 w-4 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  {{ role.permissionCount }} permissions
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(role.dateCreated) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewPermissions(role)"
                    class="text-blue-600 hover:text-blue-900 p-1 rounded"
                    title="View Permissions"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editRole(role)"
                    class="text-maneb-primary hover:text-red-700 p-1 rounded"
                    title="Edit Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteRole(role)"
                    class="text-red-600 hover:text-red-900 p-1 rounded"
                    title="Delete Role"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Simple Create Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showCreateModal = false"></div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-black mb-4">Create New Role</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                <input
                  v-model="newRoleName"
                  type="text"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary"
                  placeholder="Enter role name"
                />
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="createRole"
              class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm"
            >
              Create Role
            </button>
            <button
              @click="showCreateModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

type RecordStatus = 'Unapproved' | 'Approved' | 'SecondApproved' | 'Rejected';

// Component state
const showCreateModal = ref(false);
const newRoleName = ref('');
const isLoading = ref(false);

// Sample data for demonstration
const sampleRoles = ref([
  {
    id: 'ROL001',
    name: 'Administrator',
    status: 'Approved' as RecordStatus,
    permissionCount: 15,
    dateCreated: new Date('2024-01-15')
  },
  {
    id: 'ROL002',
    name: 'Examiner',
    status: 'SecondApproved' as RecordStatus,
    permissionCount: 8,
    dateCreated: new Date('2024-01-20')
  },
  {
    id: 'ROL003',
    name: 'Data Entry Clerk',
    status: 'Approved' as RecordStatus,
    permissionCount: 5,
    dateCreated: new Date('2024-02-01')
  },
  {
    id: 'ROL004',
    name: 'Viewer',
    status: 'Unapproved' as RecordStatus,
    permissionCount: 2,
    dateCreated: new Date('2024-02-10')
  }
]);

// Breadcrumbs
const roleBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Roles', href: '/admin/user-management/roles', current: true },
];

// Methods
const getStatusBadgeClass = (status: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800';
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800';
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800';
    case 'Rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatDate = (date: Date) => {
  return date.toLocaleDateString();
};

const createRole = () => {
  if (newRoleName.value.trim()) {
    const newRole = {
      id: `ROL${String(sampleRoles.value.length + 1).padStart(3, '0')}`,
      name: newRoleName.value.trim(),
      status: 'Unapproved' as RecordStatus,
      permissionCount: 0,
      dateCreated: new Date()
    };
    sampleRoles.value.push(newRole);
    newRoleName.value = '';
    showCreateModal.value = false;
    alert('Role created successfully!');
  }
};

const editRole = (role: any) => {
  alert(`Edit role: ${role.name} (Feature coming soon)`);
};

const deleteRole = (role: any) => {
  if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
    const index = sampleRoles.value.findIndex(r => r.id === role.id);
    if (index !== -1) {
      sampleRoles.value.splice(index, 1);
      alert('Role deleted successfully!');
    }
  }
};

const viewPermissions = (role: any) => {
  alert(`View permissions for role: ${role.name} (Feature coming soon)`);
};
</script>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary {
  background-color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}
</style>
