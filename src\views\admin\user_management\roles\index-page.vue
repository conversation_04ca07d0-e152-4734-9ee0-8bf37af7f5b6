<template>
  <component :is="layoutToUse">
    <h1>Welcome to our Application!</h1>
    <p>This is the main content for the register page.</p>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';
// import { useWindowSize } from '@/utils/window_size';
// import MobileHomeLayout from '@/views/auth/login/layouts/mobile-login-layout.vue';
// import DesktopHomeLayout from '@/views/auth/login/layouts/desktop-login-layout.vue';
// import TabletHomeLayout from '@/views/auth/login/layouts/tablet-login-layout.vue';
import DefaultRolesLayout from '@/views/admin/user_management/roles/layouts/default-roles-layout.vue';
// import SigelegeForgotPasswordLayout from '@/views/auth/forgot_password/layouts/sigelege-forgot-password-layout.vue';


// Assuming you have a way to get the current tenant, e.g., from a Pinia store or prop
// import { useTenantStore } from '@/store'; // Example tenant store

// const tenantStore = useTenantStore();
// const currentTenant = computed(() => tenantStore.currentTenant); // e.g., 'acme', 'globex'

// const { windowWidth } = useWindowSize();

const layoutToUse = computed(() => {
  // Tenant-specific layout overrides
  // if (currentTenant.value === 'acme') {
  //   if (windowWidth.value < 768) return MobileHomeLayout;
  //   if (windowWidth.value >= 768 && windowWidth.value < 1024) return TabletHomeLayout;
  //   return DesktopHomeLayout;
  // }

  // if (currentTenant.value === 'globex') {
  //   // Globex might have a simpler responsive layout
  //   if (windowWidth.value < 992) return MobileHomeLayout; // Different breakpoint for globex
  //   return DesktopHomeLayout;
  // }

  // Default responsive layouts if no tenant-specific override
  // if (windowWidth.value < 768) return MobileHomeLayout;
  // if (windowWidth.value >= 768 && windowWidth.value < 1024) return TabletHomeLayout;
  // return DesktopHomeLayout;

  return DefaultRolesLayout;
});
</script>
