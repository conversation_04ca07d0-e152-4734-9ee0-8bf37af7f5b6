<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
      <div class="bg-white rounded-lg shadow-sm p-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">Exam Schedule</h1>
        <div class="text-center py-12">
          <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Exam Schedule Coming Soon</h3>
          <p class="text-gray-500">View your upcoming examination schedule here.</p>
          <router-link to="/student/dashboard" class="mt-4 inline-block text-maneb-primary hover-text-maneb-primary-dark">
            ← Back to Dashboard
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Placeholder component for exam schedule
</script>

<style scoped>
.text-maneb-primary {
  color: #a12c2c;
}

.hover-text-maneb-primary-dark:hover {
  color: #8b2424;
}
</style>
