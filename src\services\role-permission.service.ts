import apiClient from './api-client';
import type { 
  RolePermissionDto, 
  CreateRolePermissionRequest, 
  UpdateRolePermissionRequest,
  RolePermissionFilterDto,
  BulkRolePermissionRequest 
} from '@/interfaces';

export class RolePermissionService {
  /**
   * Get all role permissions
   */
  async getAllRolePermissions(): Promise<RolePermissionDto[]> {
    try {
      return await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get role permission by ID
   */
  async getRolePermissionById(id: string): Promise<RolePermissionDto> {
    try {
      return await apiClient.get<RolePermissionDto>(`/api/RolePermission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permission');
    }
  }

  /**
   * Create new role permission
   */
  async createRolePermission(rolePermissionData: CreateRolePermissionRequest): Promise<RolePermissionDto> {
    try {
      return await apiClient.post<RolePermissionDto>('/api/RolePermission', rolePermissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create role permission');
    }
  }

  /**
   * Update existing role permission
   */
  async updateRolePermission(id: string, rolePermissionData: UpdateRolePermissionRequest): Promise<RolePermissionDto> {
    try {
      return await apiClient.put<RolePermissionDto>(`/api/RolePermission/${id}`, rolePermissionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update role permission');
    }
  }

  /**
   * Delete role permission
   */
  async deleteRolePermission(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/RolePermission/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role permission');
    }
  }

  /**
   * Get role permissions by role ID
   */
  async getRolePermissionsByRoleId(roleId: string): Promise<RolePermissionDto[]> {
    try {
      const allRolePermissions = await this.getAllRolePermissions();
      return allRolePermissions.filter(rp => rp.roleId === roleId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions by role');
    }
  }

  /**
   * Get role permissions by section ID
   */
  async getRolePermissionsBySectionId(sectionId: string): Promise<RolePermissionDto[]> {
    try {
      const allRolePermissions = await this.getAllRolePermissions();
      return allRolePermissions.filter(rp => rp.sectionID === sectionId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions by section');
    }
  }

  /**
   * Filter role permissions
   */
  async filterRolePermissions(filters: RolePermissionFilterDto): Promise<RolePermissionDto[]> {
    try {
      let rolePermissions = await this.getAllRolePermissions();

      if (filters.roleId && filters.roleId !== 'All') {
        rolePermissions = rolePermissions.filter(rp => rp.roleId === filters.roleId);
      }

      if (filters.sectionId && filters.sectionId !== 'All') {
        rolePermissions = rolePermissions.filter(rp => rp.sectionID === filters.sectionId);
      }

      if (filters.action && filters.action !== 'All') {
        rolePermissions = rolePermissions.filter(rp => rp.action === filters.action);
      }

      if (filters.canAccess !== undefined && filters.canAccess !== 'All') {
        rolePermissions = rolePermissions.filter(rp => rp.canAccess === filters.canAccess);
      }

      if (filters.status && filters.status !== 'All') {
        rolePermissions = rolePermissions.filter(rp => rp.status === filters.status);
      }

      return rolePermissions;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter role permissions');
    }
  }

  /**
   * Bulk create role permissions
   */
  async bulkCreateRolePermissions(request: BulkRolePermissionRequest): Promise<RolePermissionDto[]> {
    try {
      const createdPermissions: RolePermissionDto[] = [];
      
      for (const permission of request.permissions) {
        const created = await this.createRolePermission(permission);
        createdPermissions.push(created);
      }
      
      return createdPermissions;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk create role permissions');
    }
  }

  /**
   * Delete all role permissions for a role
   */
  async deleteRolePermissionsByRoleId(roleId: string): Promise<void> {
    try {
      const rolePermissions = await this.getRolePermissionsByRoleId(roleId);
      
      for (const rp of rolePermissions) {
        if (rp.id) {
          await this.deleteRolePermission(rp.id);
        }
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role permissions by role');
    }
  }

  /**
   * Delete all role permissions for a section
   */
  async deleteRolePermissionsBySectionId(sectionId: string): Promise<void> {
    try {
      const rolePermissions = await this.getRolePermissionsBySectionId(sectionId);
      
      for (const rp of rolePermissions) {
        if (rp.id) {
          await this.deleteRolePermission(rp.id);
        }
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role permissions by section');
    }
  }

  /**
   * Check if role has permission for section and action
   */
  async checkRolePermission(roleId: string, sectionId: string, action: string): Promise<boolean> {
    try {
      const rolePermissions = await this.getRolePermissionsByRoleId(roleId);
      const permission = rolePermissions.find(rp => 
        rp.sectionID === sectionId && 
        (rp.action === action || rp.action === 'All')
      );
      
      return permission ? permission.canAccess : false;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to check role permission');
    }
  }

  /**
   * Get role permissions with pagination
   */
  async getRolePermissionsPaginated(page: number = 1, limit: number = 10): Promise<{
    rolePermissions: RolePermissionDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allRolePermissions = await this.getAllRolePermissions();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        rolePermissions: allRolePermissions.slice(startIndex, endIndex),
        total: allRolePermissions.length,
        page,
        totalPages: Math.ceil(allRolePermissions.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated role permissions');
    }
  }

  /**
   * Search role permissions
   */
  async searchRolePermissions(query: string): Promise<RolePermissionDto[]> {
    try {
      const rolePermissions = await this.getAllRolePermissions();
      const searchTerm = query.toLowerCase();
      
      return rolePermissions.filter(rp => 
        rp.action?.toLowerCase().includes(searchTerm) ||
        rp.roleId?.toLowerCase().includes(searchTerm) ||
        rp.sectionID?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search role permissions');
    }
  }
}

export const rolePermissionService = new RolePermissionService();
