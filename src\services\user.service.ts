import apiClient from './api-client';
import type { UserDto, CreateUserRequest, UpdateUserRequest } from '@/interfaces';

export class UserService {
  /**
   * Get all users
   */
  async getAllUsers(): Promise<UserDto[]> {
    try {
      return await apiClient.get<UserDto[]>('/api/User');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<UserDto> {
    try {
      return await apiClient.get<UserDto>(`/api/User/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user');
    }
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserRequest): Promise<UserDto> {
    try {
      return await apiClient.post<UserDto>('/api/User', userData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  /**
   * Update user by ID
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<void> {
    try {
      await apiClient.put(`/api/User/${id}`, userData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  /**
   * Delete user by ID
   */
  async deleteUser(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/User/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  /**
   * Get users by status
   */
  async getUsersByStatus(status: string): Promise<UserDto[]> {
    try {
      const users = await this.getAllUsers();
      return users.filter(user => user.status === status);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users by status');
    }
  }

  /**
   * Update user status
   */
  async updateUserStatus(id: string, status: string): Promise<void> {
    try {
      await this.updateUser(id, { status: status as any });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update user status');
    }
  }

  /**
   * Search users by name or email
   */
  async searchUsers(query: string): Promise<UserDto[]> {
    try {
      const users = await this.getAllUsers();
      const searchTerm = query.toLowerCase();
      
      return users.filter(user => 
        user.firstName?.toLowerCase().includes(searchTerm) ||
        user.lastName?.toLowerCase().includes(searchTerm) ||
        user.email?.toLowerCase().includes(searchTerm) ||
        user.fullName?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search users');
    }
  }

  /**
   * Get users with pagination
   */
  async getUsersPaginated(page: number = 1, limit: number = 10): Promise<{
    users: UserDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allUsers = await this.getAllUsers();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        users: allUsers.slice(startIndex, endIndex),
        total: allUsers.length,
        page,
        totalPages: Math.ceil(allUsers.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated users');
    }
  }
}

export const userService = new UserService();
