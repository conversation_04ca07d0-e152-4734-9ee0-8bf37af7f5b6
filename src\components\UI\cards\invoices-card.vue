<template>
  <div class="bg-white p-6 rounded-2xl shadow-md w-full max-w-md">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold text-gray-800">Invoices</h2>
      <button class="text-teal-500 font-semibold text-sm px-4 py-2 rounded-lg border border-teal-500 hover:bg-teal-500 hover:text-white transition-colors duration-200">
        VIEW ALL
      </button>
    </div>

    <!-- Invoices List -->
    <ul>
      <li v-for="(invoice, index) in props.invoices" :key="index" class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
        <div>
          <span class="font-medium text-gray-800 block">{{ invoice.date }}</span>
          <span class="text-gray-500 text-xs">{{ invoice.invoiceId }}</span>
        </div>
        <div class="flex items-center space-x-4">
          <span class="font-medium text-gray-800">{{ invoice.amount }}</span>
          <a href="#" class="flex items-center text-gray-500 text-sm hover:text-teal-500 transition-colors duration-200">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm-1 15h-2v-2h2v2zm0-4h-2V8h2v5zm-2-9V4l4 4h-4z"></path>
            </svg>
            PDF
          </a>
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';

const props = defineProps({
  invoices: {
    type: Array,
    default: () => [] // Array of invoice objects
  }
})

</script>
