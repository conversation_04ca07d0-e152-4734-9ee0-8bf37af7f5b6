<template>
  <div>
    <!-- MANEB Score Validation Modal -->
    <div 
      v-if="isOpen"
      class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
      @click.self="closeModal"
    >
      <div class="relative w-full max-w-3xl max-h-full">
        <div class="relative bg-white rounded-lg shadow">
          <!-- Modal header -->
          <div class="flex items-start justify-between p-4 border-b rounded-t">
            <h3 class="text-xl font-semibold text-gray-900">
              MANEB Score Entry Validation
            </h3>
            <button 
              @click="closeModal"
              type="button" 
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
            >
              <svg class="w-3 h-3" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
            </button>
          </div>
          
          <!-- Modal body -->
          <div class="p-6 space-y-6">
            <!-- MANEB Compliance Checklist -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-blue-900 mb-3">MANEB Standard Operating Procedures (SOPs)</h4>
              <div class="space-y-2 text-sm">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-blue-800">Score range validation (0-100)</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-blue-800">Grade boundary compliance</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-blue-800">Audit trail recording</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-blue-800">Authorization level verification</span>
                </div>
              </div>
            </div>

            <!-- Score Details -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Student ID</label>
                <div class="text-sm text-black">{{ scoreData?.studentId }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Exam Number</label>
                <div class="text-sm text-black">{{ scoreData?.examNumber }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                <div class="text-sm text-black">{{ scoreData?.subject }}</div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Proposed Score</label>
                <div class="text-sm font-medium" :class="getScoreColor(scoreData?.score)">
                  {{ scoreData?.score }}%
                </div>
              </div>
            </div>

            <!-- MANEB Grade Boundaries -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-3">MANEB Grade Boundaries</h4>
              <div class="grid grid-cols-5 gap-2 text-xs">
                <div class="text-center p-2 bg-green-100 rounded">
                  <div class="font-medium text-green-800">Grade A</div>
                  <div class="text-green-600">80-100%</div>
                </div>
                <div class="text-center p-2 bg-blue-100 rounded">
                  <div class="font-medium text-blue-800">Grade B</div>
                  <div class="text-blue-600">70-79%</div>
                </div>
                <div class="text-center p-2 bg-yellow-100 rounded">
                  <div class="font-medium text-yellow-800">Grade C</div>
                  <div class="text-yellow-600">60-69%</div>
                </div>
                <div class="text-center p-2 bg-orange-100 rounded">
                  <div class="font-medium text-orange-800">Grade D</div>
                  <div class="text-orange-600">50-59%</div>
                </div>
                <div class="text-center p-2 bg-red-100 rounded">
                  <div class="font-medium text-red-800">Grade F</div>
                  <div class="text-red-600">0-49%</div>
                </div>
              </div>
            </div>

            <!-- Calculated Grade -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-green-900">Calculated Grade</h4>
                  <p class="text-sm text-green-700">Based on MANEB grade boundaries</p>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold" :class="getGradeColor(scoreData?.score)">
                    {{ calculateGrade(scoreData?.score) }}
                  </div>
                  <div class="text-sm text-gray-600">{{ getGradeDescription(scoreData?.score) }}</div>
                </div>
              </div>
            </div>

            <!-- Validation Warnings -->
            <div v-if="validationWarnings.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-yellow-900 mb-2">Validation Warnings</h4>
              <ul class="text-sm text-yellow-800 space-y-1">
                <li v-for="warning in validationWarnings" :key="warning" class="flex items-start">
                  <svg class="w-4 h-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  {{ warning }}
                </li>
              </ul>
            </div>

            <!-- Authorization Confirmation -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-red-900 mb-3">Authorization Required</h4>
              <div class="space-y-3">
                <div class="flex items-center">
                  <input 
                    id="confirm-authority" 
                    type="checkbox" 
                    v-model="confirmations.authority"
                    class="w-4 h-4 text-maneb-primary bg-gray-100 border-gray-300 rounded focus:ring-maneb-primary focus:ring-2"
                  />
                  <label for="confirm-authority" class="ml-2 text-sm text-red-800">
                    I confirm I have the authority to enter/modify scores for this examination
                  </label>
                </div>
                <div class="flex items-center">
                  <input 
                    id="confirm-accuracy" 
                    type="checkbox" 
                    v-model="confirmations.accuracy"
                    class="w-4 h-4 text-maneb-primary bg-gray-100 border-gray-300 rounded focus:ring-maneb-primary focus:ring-2"
                  />
                  <label for="confirm-accuracy" class="ml-2 text-sm text-red-800">
                    I confirm the score is accurate and follows MANEB marking schemes
                  </label>
                </div>
                <div class="flex items-center">
                  <input 
                    id="confirm-audit" 
                    type="checkbox" 
                    v-model="confirmations.audit"
                    class="w-4 h-4 text-maneb-primary bg-gray-100 border-gray-300 rounded focus:ring-maneb-primary focus:ring-2"
                  />
                  <label for="confirm-audit" class="ml-2 text-sm text-red-800">
                    I understand this action will be recorded in the audit trail
                  </label>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t">
              <button
                @click="closeModal"
                type="button"
                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                @click="confirmScoreEntry"
                type="button"
                :disabled="!allConfirmationsChecked || isProcessing"
                class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <span v-if="isProcessing" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
                <span v-else>Confirm & Save Score</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  scoreData?: {
    studentId: string
    examNumber: string
    subject: string
    score: number
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  scoreData: null
})

// Emits
const emit = defineEmits<{
  close: []
  confirmed: [data: any]
}>()

// Reactive state
const confirmations = ref({
  authority: false,
  accuracy: false,
  audit: false
})

const isProcessing = ref(false)

// Computed properties
const allConfirmationsChecked = computed(() => {
  return confirmations.value.authority && 
         confirmations.value.accuracy && 
         confirmations.value.audit
})

const validationWarnings = computed(() => {
  const warnings: string[] = []
  
  if (props.scoreData?.score !== undefined) {
    if (props.scoreData.score < 30) {
      warnings.push('Score is below 30% - please verify marking accuracy')
    }
    if (props.scoreData.score > 95) {
      warnings.push('Score is above 95% - please verify marking accuracy')
    }
    if (props.scoreData.score % 1 !== 0 && props.scoreData.score % 0.5 !== 0) {
      warnings.push('Score contains decimal places other than 0.5 - please verify MANEB marking scheme compliance')
    }
  }
  
  return warnings
})

// Methods
const calculateGrade = (score: number | undefined): string => {
  if (score === undefined) return 'N/A'
  if (score >= 80) return 'A'
  if (score >= 70) return 'B'
  if (score >= 60) return 'C'
  if (score >= 50) return 'D'
  return 'F'
}

const getGradeDescription = (score: number | undefined): string => {
  if (score === undefined) return ''
  if (score >= 80) return 'Distinction'
  if (score >= 70) return 'Credit'
  if (score >= 60) return 'Pass'
  if (score >= 50) return 'Pass'
  return 'Fail'
}

const getScoreColor = (score: number | undefined): string => {
  if (score === undefined) return 'text-gray-400'
  if (score >= 80) return 'text-green-600'
  if (score >= 70) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  if (score >= 50) return 'text-orange-600'
  return 'text-red-600'
}

const getGradeColor = (score: number | undefined): string => {
  if (score === undefined) return 'text-gray-400'
  if (score >= 80) return 'text-green-700'
  if (score >= 70) return 'text-blue-700'
  if (score >= 60) return 'text-yellow-700'
  if (score >= 50) return 'text-orange-700'
  return 'text-red-700'
}

const closeModal = () => {
  confirmations.value = {
    authority: false,
    accuracy: false,
    audit: false
  }
  emit('close')
}

const confirmScoreEntry = async () => {
  if (!allConfirmationsChecked.value) {
    sweetAlert.error('Authorization Required', 'Please confirm all authorization requirements before proceeding.')
    return
  }

  isProcessing.value = true
  
  try {
    // Create audit trail entry
    const auditData = {
      action: 'SCORE_ENTRY',
      studentId: props.scoreData?.studentId,
      examNumber: props.scoreData?.examNumber,
      subject: props.scoreData?.subject,
      score: props.scoreData?.score,
      grade: calculateGrade(props.scoreData?.score),
      timestamp: new Date().toISOString(),
      authorizations: { ...confirmations.value },
      warnings: validationWarnings.value
    }

    // Mock API call for audit trail
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    emit('confirmed', auditData)
    closeModal()
    
  } catch (error) {
    console.error('Error processing score entry:', error)
    sweetAlert.error('Error', 'Failed to process score entry. Please try again.')
  } finally {
    isProcessing.value = false
  }
}
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}
</style>
