<template>
  <div>
    <div>
      <nav class="sm:hidden" aria-label="Back">
        <a href="#" class="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700">
          <ChevronLeftIcon class="-ml-1 mr-1 size-5 shrink-0 text-gray-400" aria-hidden="true" />
          Back
        </a>
      </nav>
      <nav class="hidden sm:flex" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
          <li v-for="(item, index) in props.breadcrumbs" :key="item.name">
            <div class="flex items-center">
              <ChevronRightIcon v-if="index > 0" class="size-5 shrink-0 text-gray-400" aria-hidden="true" />
              <a :href="item.href" :aria-current="item.current ? 'page' : undefined" :class="[index === 0 ? '' : 'ml-4', 'text-sm font-medium text-gray-500 hover:text-gray-700']">{{ item.name }}</a>
            </div>
          </li>
        </ol>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid'

interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

const props = defineProps({
  breadcrumbs: {
    type: Array as () => BreadcrumbItem[],
    required: true,
  },
});
</script>
