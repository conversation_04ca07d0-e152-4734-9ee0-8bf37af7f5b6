<template>
  <div class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700">
    <div class="w-full mb-1">
      <!-- Page Header -->
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a href="#" class="inline-flex items-center text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-white">
                <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <a href="#" class="ml-1 text-gray-700 hover:text-primary-600 md:ml-2 dark:text-gray-300 dark:hover:text-white">Student & Faculty Management</a>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">Students & Faculty</span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">Students & Faculty Directory</h1>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 gap-4 mb-6 sm:grid-cols-2 lg:grid-cols-4">
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ userStore.totalUsers }}</span>
              <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Enrolled</h3>
            </div>
            <div class="flex items-center justify-end flex-1 w-0 ml-5 text-base font-bold text-green-500 dark:text-green-400">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ userStore.usersByStatus.unapproved }}</span>
              <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Pending Enrollment</h3>
            </div>
            <div class="flex items-center justify-end flex-1 w-0 ml-5 text-base font-bold text-yellow-500 dark:text-yellow-400">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ userStore.usersByStatus.approved }}</span>
              <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Students</h3>
            </div>
            <div class="flex items-center justify-end flex-1 w-0 ml-5 text-base font-bold text-green-500 dark:text-green-400">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ userStore.usersByStatus.rejected }}</span>
              <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Declined Applications</h3>
            </div>
            <div class="flex items-center justify-end flex-1 w-0 ml-5 text-base font-bold text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters and Actions -->
      <div class="flex flex-col items-center justify-between p-4 space-y-3 md:flex-row md:space-y-0 md:space-x-4">
        <div class="w-full md:w-1/2">
          <form class="flex items-center">
            <label for="simple-search" class="sr-only">Search</label>
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                type="text"
                id="simple-search"
                v-model="searchQuery"
                @input="handleSearch"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="Search students & faculty..."
              >
            </div>
          </form>
        </div>

        <div class="flex flex-col items-stretch justify-end flex-shrink-0 w-full space-y-2 md:w-auto md:flex-row md:space-y-0 md:items-center md:space-x-3">
          <!-- Status Filter -->
          <div class="flex items-center w-full md:w-auto">
            <select
              v-model="statusFilter"
              @change="handleStatusFilter"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            >
              <option value="All">All Status</option>
              <option value="Unapproved">Unapproved</option>
              <option value="Approved">Approved</option>
              <option value="SecondApproved">Second Approved</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <!-- Add User Button -->
          <button
            @click="createUser"
            type="button"
            class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
          >
            <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
            </svg>
            Add Student/Faculty
          </button>
        </div>
      </div>

      <!-- Users Table -->
      <div class="flex flex-col">
        <div class="overflow-x-auto">
          <div class="inline-block min-w-full align-middle">
            <div class="overflow-hidden shadow">
              <table class="min-w-full divide-y divide-gray-200 table-fixed dark:divide-gray-600">
                <thead class="bg-gray-100 dark:bg-gray-700">
                  <tr>
                    <th scope="col" class="p-4">
                      <div class="flex items-center">
                        <input
                          id="checkbox-all"
                          aria-describedby="checkbox-1"
                          type="checkbox"
                          v-model="selectAll"
                          @change="handleSelectAll"
                          class="w-4 h-4 border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                        >
                        <label for="checkbox-all" class="sr-only">checkbox</label>
                      </div>
                    </th>
                    <th scope="col" class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400">
                      Student/Faculty
                    </th>
                    <th scope="col" class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400">
                      Enrollment Status
                    </th>
                    <th scope="col" class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400">
                      Gender
                    </th>
                    <th scope="col" class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400">
                      Registration Date
                    </th>
                    <th scope="col" class="p-4 text-xs font-medium text-left text-gray-500 uppercase dark:text-gray-400">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                  <tr v-if="userStore.isLoading" class="hover:bg-gray-100 dark:hover:bg-gray-700">
                    <td colspan="6" class="p-4 text-center">
                      <div class="flex items-center justify-center">
                        <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="ml-2">Loading students & faculty...</span>
                      </div>
                    </td>
                  </tr>

                  <tr v-else-if="userStore.filteredUsers.length === 0" class="hover:bg-gray-100 dark:hover:bg-gray-700">
                    <td colspan="6" class="p-4 text-center text-gray-500 dark:text-gray-400">
                      No students or faculty found
                    </td>
                  </tr>

                  <tr v-else v-for="user in userStore.filteredUsers" :key="user.id" class="hover:bg-gray-100 dark:hover:bg-gray-700">
                    <td class="w-4 p-4">
                      <div class="flex items-center">
                        <input
                          :id="`checkbox-${user.id}`"
                          :aria-describedby="`checkbox-${user.id}`"
                          type="checkbox"
                          v-model="selectedUsers"
                          :value="user.id"
                          class="w-4 h-4 border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                        >
                        <label :for="`checkbox-${user.id}`" class="sr-only">checkbox</label>
                      </div>
                    </td>
                    <td class="flex items-center p-4 mr-12 space-x-6 whitespace-nowrap">
                      <div class="flex items-center justify-center w-10 h-10 bg-gray-300 rounded-full dark:bg-gray-600">
                        <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                          {{ getUserInitials(user) }}
                        </span>
                      </div>
                      <div class="text-sm font-normal text-gray-500 dark:text-gray-400">
                        <div class="text-base font-semibold text-gray-900 dark:text-white">{{ user.fullName || `${user.firstName} ${user.lastName}` }}</div>
                        <div class="text-sm font-normal text-gray-500 dark:text-gray-400">{{ user.email }}</div>
                      </div>
                    </td>
                    <td class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      <div class="flex items-center">
                        <div
                          :class="getStatusBadgeClass(user.status)"
                          class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                        >
                          {{ user.status }}
                        </div>
                      </div>
                    </td>
                    <td class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {{ user.gender || 'Not specified' }}
                    </td>
                    <td class="p-4 text-base font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {{ formatDate(user.dateCreated) }}
                    </td>
                    <td class="p-4 space-x-2 whitespace-nowrap">
                      <button
                        @click="editUser(user)"
                        type="button"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                      >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                          <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
                        </svg>
                        Edit
                      </button>
                      <button
                        @click="deleteUser(user)"
                        type="button"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900"
                      >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="sticky bottom-0 right-0 items-center w-full p-4 bg-white border-t border-gray-200 sm:flex sm:justify-between dark:bg-gray-800 dark:border-gray-700">
        <div class="flex items-center mb-4 sm:mb-0">
          <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing
            <span class="font-semibold text-gray-900 dark:text-white">{{ paginationInfo.start }}-{{ paginationInfo.end }}</span>
            of
            <span class="font-semibold text-gray-900 dark:text-white">{{ paginationInfo.total }}</span>
          </span>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="w-5 h-5 mr-1 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Previous
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="inline-flex items-center justify-center flex-1 px-3 py-2 text-sm font-medium text-center text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
            <svg class="w-5 h-5 ml-1 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- User Modal -->
  <UserModal
    :is-open="showUserModal"
    :user="selectedUser"
    @close="closeUserModal"
    @success="handleUserSuccess"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store'
import UserModal from '@/components/modals/UserModal.vue'
import type { UserDto, UserStatus } from '@/interfaces'

// Store
const userStore = useUserStore()

// Reactive state
const searchQuery = ref('')
const statusFilter = ref<UserStatus | 'All'>('All')
const selectedUsers = ref<string[]>([])
const selectAll = ref(false)
const showUserModal = ref(false)
const selectedUser = ref<UserDto | null>(null)
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed properties
const totalPages = computed(() => Math.ceil(userStore.filteredUsers.length / itemsPerPage.value))

const paginationInfo = computed(() => {
  const total = userStore.filteredUsers.length
  const start = (currentPage.value - 1) * itemsPerPage.value + 1
  const end = Math.min(currentPage.value * itemsPerPage.value, total)

  return { start, end, total }
})

// Methods
const handleSearch = () => {
  userStore.searchQuery = searchQuery.value
  currentPage.value = 1 // Reset to first page when searching
}

const handleStatusFilter = () => {
  userStore.setStatusFilter(statusFilter.value)
  currentPage.value = 1 // Reset to first page when filtering
}

const handleSelectAll = () => {
  if (selectAll.value) {
    selectedUsers.value = userStore.filteredUsers.map(user => user.id!).filter(Boolean)
  } else {
    selectedUsers.value = []
  }
}

const getUserInitials = (user: UserDto): string => {
  const first = user.firstName?.charAt(0) || ''
  const last = user.lastName?.charAt(0) || ''
  return (first + last).toUpperCase()
}

const getStatusBadgeClass = (status?: UserStatus): string => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'Rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const createUser = () => {
  console.log('createUser called')
  selectedUser.value = null
  showUserModal.value = true
  console.log('showUserModal set to:', showUserModal.value)
}

const editUser = (user: UserDto) => {
  console.log('editUser called with:', user)
  selectedUser.value = user
  showUserModal.value = true
  console.log('showUserModal set to:', showUserModal.value)
}

const closeUserModal = () => {
  showUserModal.value = false
  selectedUser.value = null
}

const handleUserSuccess = (user: UserDto) => {
  // The store will be updated automatically by the UserForm component
  // Just close the modal
  closeUserModal()
}

const deleteUser = async (user: UserDto) => {
  if (!user.id) return

  if (confirm(`Are you sure you want to delete ${user.fullName || `${user.firstName} ${user.lastName}`}?`)) {
    try {
      await userStore.deleteUser(user.id)
    } catch (error) {
      console.error('Failed to delete user:', error)
    }
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Lifecycle
onMounted(async () => {
  try {
    await userStore.fetchUsers()
  } catch (error) {
    console.error('Failed to fetch users:', error)
  }
})
</script>

<style scoped>
/* Custom styles for primary colors to match Flowbite Pro */
.bg-primary-700 {
  background-color: rgb(29 78 216); /* blue-700 */
}

.hover\:bg-primary-800:hover {
  background-color: rgb(30 64 175); /* blue-800 */
}

.focus\:ring-primary-300:focus {
  --tw-ring-color: rgb(147 197 253); /* blue-300 */
}

.focus\:ring-primary-500:focus {
  --tw-ring-color: rgb(59 130 246); /* blue-500 */
}

.focus\:border-primary-500:focus {
  border-color: rgb(59 130 246); /* blue-500 */
}

.text-primary-600 {
  color: rgb(37 99 235); /* blue-600 */
}

.dark .dark\:bg-primary-600 {
  background-color: rgb(37 99 235); /* blue-600 */
}

.dark .dark\:hover\:bg-primary-700:hover {
  background-color: rgb(29 78 216); /* blue-700 */
}

.dark .dark\:focus\:ring-primary-600:focus {
  --tw-ring-color: rgb(37 99 235); /* blue-600 */
}

.dark .dark\:focus\:ring-primary-800:focus {
  --tw-ring-color: rgb(30 64 175); /* blue-800 */
}
</style>
