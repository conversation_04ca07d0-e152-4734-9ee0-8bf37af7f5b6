<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <BreadcrumbsActions :breadcrumbs="sectionBreadcrumbs" />
    <DefaultTable
      :columns="sectionColumns"
      :data="sectionData"
      title="Sections"
      :filterOptions="sectionFilterOptions"
      :radioFilters="sectionRadioFilters"
      addButtonLabel="Add New Section"
      :showAddButton="true"
    />
    <Footer />
  </div>
</template>

<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces';

const sectionBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Sections', href: '/admin/user-management/sections', current: true },
];

const sectionColumns: TableColumn[] = [
  { key: 'id', label: 'Section ID', sortable: true },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'controller', label: 'Controller', sortable: true },
  { key: 'area', label: 'Area', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
];

const sectionData: TableRowData[] = [
  { 
    id: 'SEC001', 
    name: 'User Management', 
    controller: 'User', 
    area: 'Admin',
    status: 'Approved'
  },
  { 
    id: 'SEC002', 
    name: 'Role Management', 
    controller: 'Role', 
    area: 'Admin',
    status: 'Approved'
  },
  { 
    id: 'SEC003', 
    name: 'Section Management', 
    controller: 'Section', 
    area: 'Admin',
    status: 'Approved'
  },
  { 
    id: 'SEC004', 
    name: 'Score Entry', 
    controller: 'Score', 
    area: 'Examination',
    status: 'SecondApproved'
  },
];

const sectionFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const sectionRadioFilters = [
  { label: 'All Sections', value: 'All' },
  { label: 'Admin Area', value: 'Admin' },
  { label: 'Examination Area', value: 'Examination' },
];
</script>
