// src/interfaces/Table.ts (or a similar shared types file)

export interface TableColumn {
  key: string; // The key to access the data in each row object
  label: string; // The display name for the column header
  sortable?: boolean; // Optional: true if the column can be sorted
}

export interface TableRowData {
  [key: string]: string | number | boolean | Date | null; // Allows for flexible row data structure with common data types
  // Example of more specific properties if your data structure is consistent:
  // shipment_id: string;
  // customer: string;
  // email: string;
  // total: number;
  // due_date: string;
  // status: 'Completed' | 'Failed' | 'In transit';
  // delivery_type: string;
}
