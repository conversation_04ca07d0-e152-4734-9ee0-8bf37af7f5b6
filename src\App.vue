<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
// import { useWindowSize } from '@/utils/windowSize'; // Your utility
// import { useTenantStore } from '@/stores/tenantStore'; // Your tenant store

// Import all your layout components
import DefaultLayout from '@/components/layouts/public/layout-page.vue';
import sigelegeLayoutPage from './components/layouts/admin/sigelege-layout-page.vue';
// import AuthLayout from '@/layouts/AuthLayout.vue';
// import DashboardLayout from '@/layouts/DashboardLayout.vue';
import AdminLayout from '@/components/layouts/admin/layout-page.vue';
// import TenantALayout from '@/layouts/TenantALayout.vue';
// import TenantBLayout from '@/layouts/TenantBLayout.vue';

// Map component names to actual component imports
const layouts = {
  DefaultLayout,
  sigelegeLayoutPage,
  // AuthLayout,
  // DashboardLayout,
  AdminLayout,
  // TenantALayout,
  // TenantBLayout,
  // Add more layouts here as you create them
};

const route = useRoute();
// const { windowWidth } = useWindowSize();
// const tenantStore = useTenantStore();
// const currentTenant = computed(() => tenantStore.currentTenant);

const currentLayout = computed(() => {
  // 1. Get the layout defined in the route meta
  const layoutName = route.meta.layout || 'DefaultLayout'; // Fallback to DefaultLayout

  // 2. Implement adaptive/responsive logic
  // This can override the route's defined layout based on window size.
  // For example, if a route specifies 'DashboardLayout', but on mobile,
  // you want a simplified 'MobileDashboardLayout'.
  // if (windowWidth.value < 768) {
  //   if (layoutName === 'DashboardLayout') {
  //     layoutName = 'MobileDashboardLayout'; // Assuming you have this layout
  //   }
  //   if (layoutName === 'AdminLayout') {
  //     layoutName = 'MobileAdminLayout'; // Assuming you have this layout
  //   }
  //   // Add more mobile-specific layout overrides as needed
  // } else if (windowWidth.value >= 768 && windowWidth.value < 1024) {
  //   // Tablet specific overrides
  // }

  // 3. Implement tenant-specific layout overrides
  // This will take precedence over responsive overrides if a tenant requires a unique layout
  // if (currentTenant.value === 'acme') {
  //   // If ACME needs a completely different layout for ALL routes, you could do:
  //   // return layouts['AcmeOverallLayout'] || layouts['DefaultLayout'];
  //   // Or, more granularly based on the current determined layout:
  //   if (layoutName === 'DashboardLayout') {
  //     layoutName = 'AcmeDashboardLayout'; // If Acme has a custom dashboard layout
  //   }
  // } else if (currentTenant.value === 'globex') {
  //   // Globex-specific layout logic
  //   if (layoutName === 'DefaultLayout') {
  //     layoutName = 'GlobexHomepageLayout'; // Example: Globex has a unique homepage layout
  //   }
  // }

  // Ensure the resolved layout name actually exists in our `layouts` map
  return layouts[layoutName] || layouts['DefaultLayout'];
});

// Optional: Watch for layout changes to potentially apply global styles or classes
watch(currentLayout, (newLayout, oldLayout) => {
  console.log('Layout changed from', oldLayout?.__name, 'to', newLayout?.__name);
  // You could add/remove body classes here if needed for global styling
  // document.body.className = `${newLayout.__name.toLowerCase().replace('layout', '')}-active`;
});
</script>

<template>
  <component :is="currentLayout">
    <router-view /> </component>
</template>

<style>
/* Global styles */
body {
  margin: 0;
  font-family: Arial, sans-serif;
}
</style>
