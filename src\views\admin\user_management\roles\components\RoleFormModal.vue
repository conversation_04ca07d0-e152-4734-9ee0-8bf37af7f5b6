<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <!-- Header -->
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-black" id="modal-title">
                {{ isEdit ? 'Edit Role' : 'Create New Role' }}
              </h3>
              <button
                type="button"
                @click="$emit('close')"
                class="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Form fields -->
            <div class="space-y-4">
              <!-- Role Name -->
              <div>
                <label for="roleName" class="block text-sm font-medium text-gray-700 mb-1">
                  Role Name <span class="text-red-500">*</span>
                </label>
                <input
                  id="roleName"
                  v-model="form.name"
                  type="text"
                  required
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary"
                  placeholder="Enter role name"
                />
                <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
              </div>

              <!-- Status -->
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  v-model="form.status"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-maneb-primary focus:border-maneb-primary"
                >
                  <option value="Unapproved">Unapproved</option>
                  <option value="Approved">Approved</option>
                  <option value="SecondApproved">Second Approved</option>
                  <option value="Rejected">Rejected</option>
                </select>
              </div>

              <!-- Is Deleted -->
              <div class="flex items-center">
                <input
                  id="isDeleted"
                  v-model="form.isDeleted"
                  type="checkbox"
                  class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                />
                <label for="isDeleted" class="ml-2 block text-sm text-gray-700">
                  Mark as deleted
                </label>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Saving...' : (isEdit ? 'Update Role' : 'Create Role') }}
            </button>
            <button
              type="button"
              @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import type { RoleDto, CreateRoleRequest, RecordStatus } from '@/interfaces';

// Props
interface Props {
  role?: RoleDto | null;
  isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  role: null,
  isEdit: false
});

// Emits
const emit = defineEmits<{
  close: [];
  save: [data: CreateRoleRequest];
}>();

// Form state
const form = reactive<CreateRoleRequest>({
  name: '',
  status: 'Unapproved' as RecordStatus,
  isDeleted: false
});

const errors = reactive({
  name: ''
});

const isSubmitting = ref(false);

// Watch for role changes to populate form
watch(() => props.role, (newRole) => {
  if (newRole && props.isEdit) {
    form.name = newRole.name || '';
    form.status = newRole.status || 'Unapproved';
    form.isDeleted = newRole.isDeleted || false;
  } else {
    // Reset form for new role
    form.name = '';
    form.status = 'Unapproved';
    form.isDeleted = false;
  }
  // Clear errors
  errors.name = '';
}, { immediate: true });

// Methods
const validateForm = (): boolean => {
  let isValid = true;
  
  // Reset errors
  errors.name = '';

  // Validate name
  if (!form.name.trim()) {
    errors.name = 'Role name is required';
    isValid = false;
  } else if (form.name.trim().length < 2) {
    errors.name = 'Role name must be at least 2 characters';
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;
  
  try {
    // Emit the save event with form data
    const saveData: CreateRoleRequest = {
      name: form.name.trim(),
      status: form.status,
      isDeleted: form.isDeleted
    };
    
    // Emit to parent component
    emit('save', saveData);
  } catch (error) {
    console.error('Error submitting form:', error);
  } finally {
    isSubmitting.value = false;
  }
};


</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
