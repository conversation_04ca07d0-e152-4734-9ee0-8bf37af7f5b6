import apiClient from './api-client';
import type { 
  RoleDto, 
  CreateRoleRequest, 
  UpdateRoleRequest, 
  AssignPermissionsToRoleRequest,
  RoleFilterDto 
} from '@/interfaces';

export class RoleService {
  /**
   * Get all roles
   */
  async getAllRoles(): Promise<RoleDto[]> {
    try {
      return await apiClient.get<RoleDto[]>('/api/Role');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch roles');
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string): Promise<RoleDto> {
    try {
      return await apiClient.get<RoleDto>(`/api/Role/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role');
    }
  }

  /**
   * Create new role
   */
  async createRole(roleData: CreateRoleRequest): Promise<RoleDto> {
    try {
      return await apiClient.post<RoleDto>('/api/Role', roleData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create role');
    }
  }

  /**
   * Update existing role
   */
  async updateRole(id: string, roleData: UpdateRoleRequest): Promise<RoleDto> {
    try {
      return await apiClient.put<RoleDto>(`/api/Role/${id}`, roleData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update role');
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/Role/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role');
    }
  }

  /**
   * Get role permissions by role ID
   */
  async getRolePermissions(roleId: string): Promise<RolePermissionDto[]> {
    try {
      const allRolePermissions = await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
      return allRolePermissions.filter(rp => rp.roleId === roleId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get roles with pagination
   */
  async getRolesPaginated(page: number = 1, limit: number = 10): Promise<{
    roles: RoleDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allRoles = await this.getAllRoles();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        roles: allRoles.slice(startIndex, endIndex),
        total: allRoles.length,
        page,
        totalPages: Math.ceil(allRoles.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated roles');
    }
  }

  /**
   * Search roles
   */
  async searchRoles(query: string): Promise<RoleDto[]> {
    try {
      const roles = await this.getAllRoles();
      const searchTerm = query.toLowerCase();
      
      return roles.filter(role => 
        role.name?.toLowerCase().includes(searchTerm) ||
        role.description?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search roles');
    }
  }

  /**
   * Filter roles
   */
  async filterRoles(filters: RoleFilterDto): Promise<RoleDto[]> {
    try {
      let roles = await this.getAllRoles();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        roles = roles.filter(role => 
          role.name?.toLowerCase().includes(searchTerm) ||
          role.description?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.isActive !== undefined && filters.isActive !== 'All') {
        roles = roles.filter(role => role.isActive === filters.isActive);
      }

      if (filters.hasPermissions !== undefined && filters.hasPermissions !== 'All') {
        roles = roles.filter(role => {
          const hasPerms = role.permissions && role.permissions.length > 0;
          return filters.hasPermissions ? hasPerms : !hasPerms;
        });
      }

      if (filters.userCount && filters.userCount !== 'All') {
        roles = roles.filter(role => {
          const count = role.userCount || 0;
          switch (filters.userCount) {
            case 'None': return count === 0;
            case 'Some': return count > 0 && count <= 10;
            case 'Many': return count > 10;
            default: return true;
          }
        });
      }

      return roles;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter roles');
    }
  }

  /**
   * Toggle role active status
   */
  async toggleRoleStatus(id: string): Promise<RoleDto> {
    try {
      return await apiClient.put<RoleDto>(`/api/Role/${id}/toggle-status`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle role status');
    }
  }
}

export const roleService = new RoleService();
