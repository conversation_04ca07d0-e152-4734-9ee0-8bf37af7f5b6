<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">Grading System Setup</h1>
            <p class="text-sm text-gray-600 mt-1">Configure subjects, papers, and grade boundaries for the examination system</p>
          </div>
          <div class="flex items-center space-x-3">

            <button
              @click="activeTab = 'subjects'"
              :class="[
                'px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                activeTab === 'subjects' 
                  ? 'bg-maneb-primary text-white' 
                  : 'text-gray-600 hover:bg-gray-100'
              ]"
            >
              Subjects
            </button>
            <button
              @click="activeTab = 'papers'"
              :class="[
                'px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                activeTab === 'papers'
                  ? 'bg-maneb-primary text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              ]"
            >
              Papers
            </button>
            <button
              @click="activeTab = 'boundaries'"
              :class="[
                'px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                activeTab === 'boundaries'
                  ? 'bg-maneb-primary text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              ]"
            >
              Grade Boundaries
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Subjects Tab -->
      <div v-if="activeTab === 'subjects'" class="space-y-6">
        <SubjectManagement />
      </div>

      <!-- Papers Tab -->
      <div v-if="activeTab === 'papers'" class="space-y-6">
        <PaperManagement />
      </div>

      <!-- Grade Boundaries Tab -->
      <div v-if="activeTab === 'boundaries'" class="space-y-6">
        <GradeBoundaryManagement />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGradingStore } from '@/store'
import SubjectManagement from './components/SubjectManagement.vue'
import PaperManagement from './components/PaperManagement.vue'
import GradeBoundaryManagement from './components/GradeBoundaryManagement.vue'

// Store
const gradingStore = useGradingStore()

// State
const activeTab = ref<'subjects' | 'papers' | 'boundaries'>('subjects')

// Lifecycle
onMounted(async () => {
  try {
    // Initialize store with mock data
    await gradingStore.initializeStore()
  } catch (error) {
    console.error('Failed to load grading system data:', error)
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
