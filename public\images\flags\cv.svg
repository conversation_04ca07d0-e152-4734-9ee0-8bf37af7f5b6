<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 10.6667H28V0H0V10.6667Z" fill="#0C49AE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 19.9998H28V14.6665H0V19.9998Z" fill="#0C49AE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 14.6665H28V10.6665H0V14.6665Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 13.3333H28V12H0V13.3333Z" fill="#CD232E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.6667 16.6665C12.8759 16.6665 14.6667 14.8756 14.6667 12.6665C14.6667 10.4574 12.8759 8.6665 10.6667 8.6665C8.45761 8.6665 6.66675 10.4574 6.66675 12.6665C6.66675 14.8756 8.45761 16.6665 10.6667 16.6665Z" stroke="#F7D035" stroke-width="1.33333" stroke-linecap="round" stroke-dasharray="0.67 2"/>
</g>
</svg>
