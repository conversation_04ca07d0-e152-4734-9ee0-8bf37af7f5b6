import Swal from 'sweetalert2'

// MANEB theme colors
const MANEB_COLORS = {
  primary: '#a12c2c',
  secondary: '#D97706',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6'
}

// Default SweetAlert configuration with MANEB styling
const defaultConfig = {
  confirmButtonColor: MANEB_COLORS.primary,
  cancelButtonColor: '#6B7280',
  customClass: {
    popup: 'rounded-lg shadow-2xl',
    title: 'text-gray-800 font-bold',
    content: 'text-gray-600',
    confirmButton: 'rounded-md px-4 py-2 font-medium',
    cancelButton: 'rounded-md px-4 py-2 font-medium'
  }
}

export const sweetAlert = {
  // Success notification
  success: (title: string, text?: string) => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'success',
      title,
      text,
      confirmButtonColor: MANEB_COLORS.success,
      timer: 3000,
      timerProgressBar: true
    })
  },

  // Error notification
  error: (title: string, text?: string) => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'error',
      title,
      text,
      confirmButtonColor: MANEB_COLORS.error
    })
  },

  // Warning notification
  warning: (title: string, text?: string) => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'warning',
      title,
      text,
      confirmButtonColor: MANEB_COLORS.warning
    })
  },

  // Info notification
  info: (title: string, text?: string) => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'info',
      title,
      text,
      confirmButtonColor: MANEB_COLORS.info
    })
  },

  // Confirmation dialog
  confirm: (title: string, text?: string, confirmText = 'Yes', cancelText = 'Cancel') => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'question',
      title,
      text,
      showCancelButton: true,
      confirmButtonText: confirmText,
      cancelButtonText: cancelText,
      reverseButtons: true
    })
  },

  // Delete confirmation
  confirmDelete: (title = 'Are you sure?', text = 'This action cannot be undone!') => {
    return Swal.fire({
      ...defaultConfig,
      icon: 'warning',
      title,
      text,
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      confirmButtonColor: MANEB_COLORS.error,
      reverseButtons: true
    })
  },

  // Loading/Progress
  loading: (title = 'Please wait...', text?: string) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      didOpen: () => {
        Swal.showLoading()
      }
    })
  },

  // Toast notification (small, non-intrusive)
  toast: {
    success: (message: string) => {
      return Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'success',
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        background: '#F0FDF4',
        color: '#166534'
      })
    },

    error: (message: string) => {
      return Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'error',
        title: message,
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true,
        background: '#FEF2F2',
        color: '#DC2626'
      })
    },

    warning: (message: string) => {
      return Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'warning',
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        background: '#FFFBEB',
        color: '#D97706'
      })
    },

    info: (message: string) => {
      return Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'info',
        title: message,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        background: '#EFF6FF',
        color: '#2563EB'
      })
    }
  },

  // Close any open SweetAlert
  close: () => {
    Swal.close()
  }
}

export default sweetAlert
