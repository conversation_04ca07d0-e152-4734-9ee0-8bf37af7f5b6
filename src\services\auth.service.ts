import apiClient from './api-client';
import type { LoginModel, AuthResponse, RefreshTokenRequest, UserDto } from '@/interfaces';

export class AuthService {
  /**
   * Decode JWT token and extract user information
   */
  private decodeJwtPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding JWT:', error);
      return null;
    }
  }

  /**
   * Extract user data from JWT token
   */
  private extractUserFromToken(token: string): UserDto | null {
    console.log('Extracting user from token:', token.substring(0, 50) + '...');

    const payload = this.decodeJwtPayload(token);
    if (!payload) {
      console.error('Failed to decode JWT payload');
      return null;
    }

    console.log('JWT Payload:', payload);
    console.log('Available claims:', Object.keys(payload));

    // Map JWT claims to UserDto
    const user: UserDto = {
      id: payload.sub || payload.nameid || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'],
      email: payload.email || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'],
      userName: payload.username || payload.unique_name || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'],
      firstName: payload.given_name || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname'],
      lastName: payload.family_name || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'],
      fullName: payload.full_name || payload.name || payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'],
      status: parseInt(payload.user_status) || parseInt(payload['user_status']) || 0,
      gender: payload.gender,
      isDeleted: payload.is_active === 'false' || payload.is_active === false,
      dateCreated: payload.account_created ? new Date(payload.account_created) : undefined
    };

    user.role="Admin";

    console.log('Extracted user from JWT:', user);
    console.log('User status:', user.status, 'Type:', typeof user.status);
    return user;
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginModel): Promise<AuthResponse> {
    try {
      // Make the API call
      const response = await apiClient.post('/api/login', credentials);

      console.log('Raw API response:', response);
      console.log('Response type:', typeof response);
      console.log('Response keys:', Object.keys(response || {}));

      // Handle different possible response structures
      let token: string;
      let refreshToken: string;

      if (typeof response === 'string') {
        // If response is just a JWT string
        token = response;
        refreshToken = ''; // No refresh token in this case
      } else if (response && typeof response === 'object') {
        // If response is an object, look for token in various possible properties
        token = response.token || response.accessToken || response.access_token || response.jwt;
        refreshToken = response.refreshToken || response.refresh_token || '';

        // If no token found in expected properties, check if the whole response is the token
        if (!token && typeof response === 'string') {
          token = response;
        }
      } else {
        throw new Error('Invalid response format from login API');
      }

      console.log('Extracted token:', token);
      console.log('Extracted refreshToken:', refreshToken);

      if (!token) {
        throw new Error('No token received from login API');
      }

      // Extract user data from JWT token
      const user = this.extractUserFromToken(token);

      if (!user) {
        throw new Error('Failed to extract user data from token');
      }

      // Store tokens and user data
      apiClient.setToken(token);
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken);
      }
      localStorage.setItem('user', JSON.stringify(user));

      // Return the expected AuthResponse format
      const authResponse: AuthResponse = {
        token: token,
        refreshToken: refreshToken,
        user: user,
        expiresAt: '' // We can extract this from JWT exp claim if needed
      };

      console.log('Final auth response:', authResponse);
      return authResponse;
    } catch (error: any) {
      console.error('Login error:', error);
      console.error('Error details:', error.response?.data);
      throw new Error(error.response?.data?.message || error.message || 'Login failed');
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        await apiClient.post('/api/logout', refreshToken);
      }
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      // Clear local storage regardless of API call success
      apiClient.clearToken();
      localStorage.removeItem('user');
      localStorage.removeItem('refresh_token');
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<AuthResponse> {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiClient.post<{token: string, refreshToken: string}>('/api/refresh', refreshToken);

      // Extract user data from new JWT token
      const user = this.extractUserFromToken(response.token);

      if (!user) {
        throw new Error('Failed to extract user data from refreshed token');
      }

      // Update stored tokens
      if (response.token) {
        apiClient.setToken(response.token);
        localStorage.setItem('refresh_token', response.refreshToken);
        localStorage.setItem('user', JSON.stringify(user));
      }

      // Return the expected AuthResponse format
      const authResponse: AuthResponse = {
        token: response.token,
        refreshToken: response.refreshToken,
        user: user,
        expiresAt: ''
      };

      return authResponse;
    } catch (error: any) {
      // If refresh fails, clear all auth data
      apiClient.clearToken();
      localStorage.removeItem('user');
      throw new Error(error.response?.data?.message || 'Token refresh failed');
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  }

  /**
   * Get current user from localStorage
   */
  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * Get current token
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Check if token is expired (basic check)
   */
  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) return true;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

export const authService = new AuthService();
