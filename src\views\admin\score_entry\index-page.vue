<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/admin/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
              <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
              </svg>
              Admin
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Score Entry</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Score Entry</h1>
        <p class="mt-2 text-gray-600">Enter examination scores for registered students</p>
      </div>

      <!-- Stage 1: Filtering Page -->
      <div v-if="currentStage === 'filtering'" class="space-y-8">
        <FilteringStage
          @filters-selected="handleFiltersSelected"
          :is-loading="isTransitioning"
          :current-filters="selectedFilters"
        />
      </div>

      <!-- Stage 2: Score Entry Table -->
      <div v-else-if="currentStage === 'scoreEntry'" class="space-y-6">
        <ScoreEntryStage
          :selected-filters="selectedFilters"
          @edit-filters="handleEditFilters"
          @score-updated="handleScoreUpdated"
        />
      </div>

    </div>

    <!-- Score Entry Modal (kept for compatibility) -->
    <ScoreEntryModal
      :is-open="isModalOpen"
      :student="selectedStudent"
      :exam-details="examDetails"
      @close="closeScoreModal"
      @success="handleScoreSubmitted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import FilteringStage from './components/FilteringStage.vue'
import ScoreEntryStage from './components/ScoreEntryStage.vue'
import ScoreEntryModal from './components/ScoreEntryModal.vue'
import type { UserDto } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Types
interface FilterSelection {
  division: string
  district: string
  center: string
  school: string
  subject: string
  examType: string
  examNumber: string
}

interface ExamDetails {
  examType: string
  examNumber: string
  subject: string
}

// Reactive state
const currentStage = ref<'filtering' | 'scoreEntry'>('filtering')
const selectedFilters = ref<FilterSelection | null>(null)
const isTransitioning = ref(false)
const selectedStudent = ref<UserDto | null>(null)
const isModalOpen = ref(false)

// Computed properties
const examDetails = computed((): ExamDetails => ({
  examType: selectedFilters.value?.examType || '',
  examNumber: selectedFilters.value?.examNumber || '',
  subject: selectedFilters.value?.subject || ''
}))

// Methods
const handleFiltersSelected = async (filters: FilterSelection) => {
  isTransitioning.value = true

  try {
    // Simulate API validation/loading
    await new Promise(resolve => setTimeout(resolve, 1000))

    selectedFilters.value = filters
    currentStage.value = 'scoreEntry'

    sweetAlert.success(
      'Filters Applied',
      'Score entry table is now ready for the selected criteria.'
    )
  } catch (error) {
    console.error('Error applying filters:', error)
    sweetAlert.error('Error', 'Failed to apply filters. Please try again.')
  } finally {
    isTransitioning.value = false
  }
}

const handleEditFilters = () => {
  currentStage.value = 'filtering'
  // Keep the selected filters so they can be pre-populated for editing
}

const handleScoreUpdated = (data: any) => {
  console.log('Score updated:', data)
  sweetAlert.success(
    'Score Updated',
    'The student score has been successfully updated.'
  )
}

// Modal methods (kept for compatibility)
const closeScoreModal = () => {
  isModalOpen.value = false
  selectedStudent.value = null
}

const handleScoreSubmitted = (studentId: string) => {
  closeScoreModal()
  sweetAlert.success(
    'Score Entered Successfully',
    'The student score has been recorded.'
  )
}
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
}
</style>
