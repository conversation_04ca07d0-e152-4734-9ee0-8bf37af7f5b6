<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-black" id="modal-title">
              Role Permissions: {{ role?.name }}
            </h3>
            <button
              type="button"
              @click="$emit('close')"
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
            <span class="ml-2 text-gray-600">Loading permissions...</span>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error loading permissions</h3>
                <p class="mt-1 text-sm text-red-700">{{ error }}</p>
                <button @click="loadPermissions" class="mt-2 text-sm text-red-600 hover:text-red-500 underline">
                  Try again
                </button>
              </div>
            </div>
          </div>

          <!-- Permissions Content -->
          <div v-else>
            <!-- Summary -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-maneb-primary">{{ permissions.length }}</div>
                  <div class="text-sm text-gray-600">Total Permissions</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{{ activePermissions }}</div>
                  <div class="text-sm text-gray-600">Active Permissions</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-red-600">{{ inactivePermissions }}</div>
                  <div class="text-sm text-gray-600">Inactive Permissions</div>
                </div>
              </div>
            </div>

            <!-- Permissions Table -->
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Section
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Access
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-if="permissions.length === 0">
                    <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                      No permissions assigned to this role
                    </td>
                  </tr>
                  <tr v-else v-for="permission in permissions" :key="permission.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-black">
                      {{ permission.sectionID }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {{ permission.action }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="permission.canAccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                            class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ permission.canAccess ? 'Allowed' : 'Denied' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusBadgeClass(permission.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ permission.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(permission.dateCreated) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="button"
            @click="$emit('close')"
            class="w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:w-auto sm:text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoleStore } from '@/store/role.store';
import type { RoleDto, RolePermissionDto, RecordStatus } from '@/interfaces';

// Props
interface Props {
  role?: RoleDto | null;
}

const props = withDefaults(defineProps<Props>(), {
  role: null
});

// Emits
defineEmits<{
  close: [];
}>();

// Store
const roleStore = useRoleStore();

// State
const permissions = ref<RolePermissionDto[]>([]);
const isLoading = ref(false);
const error = ref<string | null>(null);

// Computed
const activePermissions = computed(() => 
  permissions.value.filter(p => p.canAccess).length
);

const inactivePermissions = computed(() => 
  permissions.value.filter(p => !p.canAccess).length
);

// Methods
const loadPermissions = async () => {
  if (!props.role?.id) return;

  try {
    isLoading.value = true;
    error.value = null;
    permissions.value = await roleStore.getRolePermissions(props.role.id);
  } catch (err: any) {
    error.value = err.message || 'Failed to load permissions';
    console.error('Failed to load permissions:', err);
  } finally {
    isLoading.value = false;
  }
};

const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800';
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800';
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800';
    case 'Rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatDate = (date?: Date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
};

// Watch for role changes
watch(() => props.role, (newRole) => {
  if (newRole) {
    loadPermissions();
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  if (props.role) {
    loadPermissions();
  }
});
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>
