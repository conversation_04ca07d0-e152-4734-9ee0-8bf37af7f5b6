import apiClient from './api-client';
import type { 
  SectionDto, 
  CreateSectionRequest, 
  UpdateSectionRequest,
  SectionFilterDto 
} from '@/interfaces';

export class SectionService {
  /**
   * Get all sections
   */
  async getAllSections(): Promise<SectionDto[]> {
    try {
      return await apiClient.get<SectionDto[]>('/api/Section');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch sections');
    }
  }

  /**
   * Get section by ID
   */
  async getSectionById(id: string): Promise<SectionDto> {
    try {
      return await apiClient.get<SectionDto>(`/api/Section/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch section');
    }
  }

  /**
   * Create new section
   */
  async createSection(sectionData: CreateSectionRequest): Promise<SectionDto> {
    try {
      return await apiClient.post<SectionDto>('/api/Section', sectionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create section');
    }
  }

  /**
   * Update existing section
   */
  async updateSection(id: string, sectionData: UpdateSectionRequest): Promise<SectionDto> {
    try {
      return await apiClient.put<SectionDto>(`/api/Section/${id}`, sectionData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update section');
    }
  }

  /**
   * Delete section
   */
  async deleteSection(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/Section/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete section');
    }
  }

  /**
   * Get sections with pagination
   */
  async getSectionsPaginated(page: number = 1, limit: number = 10): Promise<{
    sections: SectionDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const allSections = await this.getAllSections();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return {
        sections: allSections.slice(startIndex, endIndex),
        total: allSections.length,
        page,
        totalPages: Math.ceil(allSections.length / limit)
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch paginated sections');
    }
  }

  /**
   * Search sections
   */
  async searchSections(query: string): Promise<SectionDto[]> {
    try {
      const sections = await this.getAllSections();
      const searchTerm = query.toLowerCase();
      
      return sections.filter(section => 
        section.name?.toLowerCase().includes(searchTerm) ||
        section.description?.toLowerCase().includes(searchTerm) ||
        section.code?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search sections');
    }
  }

  /**
   * Filter sections
   */
  async filterSections(filters: SectionFilterDto): Promise<SectionDto[]> {
    try {
      let sections = await this.getAllSections();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        sections = sections.filter(section => 
          section.name?.toLowerCase().includes(searchTerm) ||
          section.description?.toLowerCase().includes(searchTerm) ||
          section.code?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.parentSectionId && filters.parentSectionId !== 'All') {
        sections = sections.filter(section => section.parentSectionId === filters.parentSectionId);
      }

      if (filters.level !== undefined && filters.level !== 'All') {
        sections = sections.filter(section => section.level === filters.level);
      }

      if (filters.isActive !== undefined && filters.isActive !== 'All') {
        sections = sections.filter(section => section.isActive === filters.isActive);
      }

      if (filters.hasUsers !== undefined && filters.hasUsers !== 'All') {
        sections = sections.filter(section => {
          const hasUsers = section.userCount && section.userCount > 0;
          return filters.hasUsers ? hasUsers : !hasUsers;
        });
      }

      return sections;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter sections');
    }
  }

  /**
   * Get root sections (level 0)
   */
  async getRootSections(): Promise<SectionDto[]> {
    try {
      const sections = await this.getAllSections();
      return sections.filter(section => section.level === 0);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch root sections');
    }
  }

  /**
   * Get child sections by parent ID
   */
  async getChildSections(parentId: string): Promise<SectionDto[]> {
    try {
      const sections = await this.getAllSections();
      return sections.filter(section => section.parentSectionId === parentId);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch child sections');
    }
  }

  /**
   * Get section hierarchy (tree structure)
   */
  async getSectionHierarchy(): Promise<SectionDto[]> {
    try {
      const allSections = await this.getAllSections();
      
      // Build hierarchy by organizing sections into tree structure
      const sectionMap = new Map<string, SectionDto>();
      const rootSections: SectionDto[] = [];

      // First pass: create map of all sections
      allSections.forEach(section => {
        if (section.id) {
          sectionMap.set(section.id, { ...section, childSections: [] });
        }
      });

      // Second pass: build hierarchy
      allSections.forEach(section => {
        if (section.id) {
          const sectionWithChildren = sectionMap.get(section.id);
          if (sectionWithChildren) {
            if (section.parentSectionId) {
              const parent = sectionMap.get(section.parentSectionId);
              if (parent && parent.childSections) {
                parent.childSections.push(sectionWithChildren);
              }
            } else {
              rootSections.push(sectionWithChildren);
            }
          }
        }
      });

      return rootSections;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch section hierarchy');
    }
  }

  /**
   * Get section path (breadcrumb trail)
   */
  async getSectionPath(sectionId: string): Promise<SectionDto[]> {
    try {
      const allSections = await this.getAllSections();
      const sectionMap = new Map<string, SectionDto>();
      
      allSections.forEach(section => {
        if (section.id) {
          sectionMap.set(section.id, section);
        }
      });

      const path: SectionDto[] = [];
      let currentSection = sectionMap.get(sectionId);

      while (currentSection) {
        path.unshift(currentSection);
        currentSection = currentSection.parentSectionId ? 
          sectionMap.get(currentSection.parentSectionId) : undefined;
      }

      return path;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch section path');
    }
  }

  /**
   * Toggle section active status
   */
  async toggleSectionStatus(id: string): Promise<SectionDto> {
    try {
      return await apiClient.put<SectionDto>(`/api/Section/${id}/toggle-status`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to toggle section status');
    }
  }

  /**
   * Move section to different parent
   */
  async moveSection(sectionId: string, newParentId: string | null): Promise<SectionDto> {
    try {
      return await apiClient.put<SectionDto>(`/api/Section/${sectionId}/move`, {
        newParentId
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to move section');
    }
  }
}

export const sectionService = new SectionService();
