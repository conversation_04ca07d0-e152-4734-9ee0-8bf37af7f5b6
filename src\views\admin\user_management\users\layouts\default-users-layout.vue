<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'

const userBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: true },
];
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces/table';

const userColumns: TableColumn[] = [
  { key: 'user_id', label: 'User ID', sortable: true },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
];

const userData: TableRowData[] = [
  { user_id: 'USR001', name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active' },
  { user_id: 'USR002', name: 'Bob Johnson', email: '<EMAIL>', role: 'User', status: 'Inactive' },
  { user_id: 'USR003', name: 'Charlie Brown', email: '<EMAIL>', role: 'Editor', status: 'Active' },
  { user_id: 'USR004', name: 'Diana Prince', email: '<EMAIL>', role: 'User', status: 'Active' },
  { user_id: 'USR005', name: 'Eve Adams', email: '<EMAIL>', role: 'Admin', status: 'Inactive' },
];

const userFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const userRadioFilters = [
  { label: 'All Users', value: 'All' },
  { label: 'Active Users', value: 'Active' },
  { label: 'Inactive Users', value: 'Inactive' },
];


</script>

<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <BreadcrumbsActions :breadcrumbs="userBreadcrumbs" />
        <DefaultTable
          :columns="userColumns"
          :data="userData"
          title="Users"
          :filterOptions="userFilterOptions"
          :radioFilters="userRadioFilters"
          addButtonLabel="Add New User"
          :showAddButton="true"
        />
        <Footer />
      </div>
</template>
