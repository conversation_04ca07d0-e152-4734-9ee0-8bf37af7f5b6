<template>
  <!-- Flowbite Pro Modal -->
  <div 
    v-if="isOpen"
    id="score-entry-modal" 
    tabindex="-1" 
    aria-hidden="true" 
    class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
    @click.self="closeModal"
  >
    <div class="relative w-full max-w-2xl max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow">
        <!-- Modal header -->
        <div class="flex items-start justify-between p-4 border-b rounded-t">
          <h3 class="text-xl font-semibold text-gray-900">
            Enter Score
          </h3>
          <button 
            @click="closeModal"
            type="button" 
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
          >
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        
        <!-- Modal body -->
        <div class="p-6 space-y-6">
          <!-- Student Information -->
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Student Information</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600">Student ID:</span>
                <span class="ml-2 font-medium text-black">{{ student?.userName }}</span>
              </div>
              <div>
                <span class="text-gray-600">Full Name:</span>
                <span class="ml-2 font-medium text-black">{{ student?.firstName }} {{ student?.lastName }}</span>
              </div>
              <div>
                <span class="text-gray-600">Email:</span>
                <span class="ml-2 font-medium text-black">{{ student?.email }}</span>
              </div>
              <div>
                <span class="text-gray-600">Status:</span>
                <span class="ml-2 font-medium text-green-600">{{ student?.status }}</span>
              </div>
            </div>
          </div>

          <!-- Exam Information -->
          <div class="bg-blue-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Exam Information</h4>
            <div class="grid grid-cols-1 gap-2 text-sm">
              <div>
                <span class="text-gray-600">Exam Type:</span>
                <span class="ml-2 font-medium text-black">{{ examDetails?.examType }}</span>
              </div>
              <div>
                <span class="text-gray-600">Exam Number:</span>
                <span class="ml-2 font-medium text-black">{{ examDetails?.examNumber }}</span>
              </div>
              <div>
                <span class="text-gray-600">Subject:</span>
                <span class="ml-2 font-medium text-black">{{ examDetails?.subject }}</span>
              </div>
            </div>
          </div>

          <!-- Score Entry Form -->
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Score Input -->
            <div>
              <label for="score" class="block mb-2 text-sm font-medium text-gray-900">
                Score <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input 
                  type="number" 
                  id="score" 
                  v-model="formData.score"
                  min="0"
                  max="100"
                  step="0.1"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5" 
                  placeholder="Enter score (0-100)"
                  required
                  :disabled="isLoading"
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span class="text-gray-500 text-sm">/ 100</span>
                </div>
              </div>
              <p class="mt-1 text-xs text-gray-500">Enter the student's score as a percentage (0-100)</p>
            </div>

            <!-- Grade Display -->
            <div v-if="formData.score !== null && formData.score !== ''" class="bg-green-50 border border-green-200 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <span class="text-green-800 text-sm font-medium">Calculated Grade: </span>
                    <span class="font-bold" :class="getGradeColor(formData.score)">{{ calculateGrade(formData.score) }}</span>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold" :class="getGradeColor(formData.score)">{{ formData.score }}%</div>
                </div>
              </div>
            </div>

            <!-- Score Type Selection -->
            <div>
              <label class="block mb-2 text-sm font-medium text-gray-900">
                Score Type <span class="text-red-500">*</span>
              </label>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center">
                  <input 
                    id="final-score" 
                    type="radio" 
                    value="final" 
                    v-model="formData.scoreType"
                    name="score-type" 
                    class="w-4 h-4 text-maneb-primary bg-gray-100 border-gray-300 focus:ring-maneb-primary focus:ring-2"
                    required
                  />
                  <label for="final-score" class="ml-2 text-sm font-medium text-gray-900">Final Score</label>
                </div>
                <div class="flex items-center">
                  <input 
                    id="provisional-score" 
                    type="radio" 
                    value="provisional" 
                    v-model="formData.scoreType"
                    name="score-type" 
                    class="w-4 h-4 text-maneb-primary bg-gray-100 border-gray-300 focus:ring-maneb-primary focus:ring-2"
                  />
                  <label for="provisional-score" class="ml-2 text-sm font-medium text-gray-900">Provisional Score</label>
                </div>
              </div>
            </div>

            <!-- Remarks -->
            <div>
              <label for="remarks" class="block mb-2 text-sm font-medium text-gray-900">
                Remarks (Optional)
              </label>
              <textarea 
                id="remarks" 
                v-model="formData.remarks"
                rows="3"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5" 
                placeholder="Enter any additional remarks or notes..."
                :disabled="isLoading"
              ></textarea>
            </div>

            <!-- Error Display -->
            <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-red-800 text-sm">{{ error }}</span>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t">
              <button
                @click="closeModal"
                type="button"
                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
                :disabled="isLoading"
              >
                Cancel
              </button>
              <button
                type="submit"
                class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                :disabled="isLoading || !isFormValid"
              >
                <span v-if="isLoading" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </span>
                <span v-else>Submit Score</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { UserDto } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  student?: UserDto | null
  examDetails?: {
    examType: string
    examNumber: string
    subject: string
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  student: null,
  examDetails: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: [studentId: string]
}>()

// Reactive state
const formData = ref({
  score: null as number | null,
  scoreType: 'final',
  remarks: ''
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed properties
const isFormValid = computed(() => {
  return formData.value.score !== null && 
         formData.value.score >= 0 && 
         formData.value.score <= 100 &&
         formData.value.scoreType
})

// Methods
const calculateGrade = (score: number | string): string => {
  const numScore = typeof score === 'string' ? parseFloat(score) : score
  if (isNaN(numScore)) return 'N/A'

  if (numScore >= 80) return 'A (Distinction)'
  if (numScore >= 70) return 'B (Credit)'
  if (numScore >= 60) return 'C (Pass)'
  if (numScore >= 50) return 'D (Pass)'
  return 'F (Fail)'
}

const getGradeColor = (score: number | string): string => {
  const numScore = typeof score === 'string' ? parseFloat(score) : score
  if (isNaN(numScore)) return 'text-gray-500'

  if (numScore >= 80) return 'text-green-700'
  if (numScore >= 70) return 'text-blue-700'
  if (numScore >= 60) return 'text-yellow-700'
  if (numScore >= 50) return 'text-orange-700'
  return 'text-red-700'
}

const resetForm = () => {
  formData.value = {
    score: null,
    scoreType: 'final',
    remarks: ''
  }
  error.value = null
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value || !props.student) return

  isLoading.value = true
  error.value = null

  try {
    // Mock API call - replace with actual implementation
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simulate API call to save score
    const scoreData = {
      studentId: props.student.id,
      examType: props.examDetails?.examType,
      examNumber: props.examDetails?.examNumber,
      subject: props.examDetails?.subject,
      score: formData.value.score,
      scoreType: formData.value.scoreType,
      remarks: formData.value.remarks,
      grade: calculateGrade(formData.value.score!),
      enteredAt: new Date().toISOString()
    }

    console.log('Score entry data:', scoreData)
    
    // Emit success event
    emit('success', props.student.id)
    
  } catch (err) {
    error.value = 'Failed to submit score. Please try again.'
    console.error('Score submission error:', err)
  } finally {
    isLoading.value = false
  }
}

// Watch for modal open/close to handle body scroll
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
    resetForm()
  } else {
    document.body.style.overflow = 'auto'
  }
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}
</style>
