<script setup lang="ts">
import BreadcrumbsActions from '@/components/UI/breadcrumbs/breadcrumbs_actions.vue'

const auditBreadcrumbs = [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: '<PERSON><PERSON>', href: '/admin/user-management/audits', current: true },
];
import DefaultTable from '@/components/UI/tables/default-table.vue';
import Footer from '@/components/UI/footers/footer-one.vue';
import type { TableColumn, TableRowData } from '@/interfaces/table';

const auditColumns: TableColumn[] = [
  { key: 'audit_id', label: 'Audit ID', sortable: true },
  { key: 'user', label: 'User', sortable: true },
  { key: 'action', label: 'Action', sortable: true },
  { key: 'timestamp', label: 'Timestamp', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
];

const auditData: TableRowData[] = [
  { audit_id: 'AUD001', user: '<PERSON>', action: 'Login', timestamp: '2023-10-26 10:00:00', status: 'Success' },
  { audit_id: 'AUD002', user: 'Bob Johnson', action: 'Create User', timestamp: '2023-10-26 10:05:15', status: 'Success' },
  { audit_id: 'AUD003', user: 'Charlie Brown', action: 'Delete User', timestamp: '2023-10-26 10:10:30', status: 'Failed' },
  { audit_id: 'AUD004', user: 'Diana Prince', action: 'Update Role', timestamp: '2023-10-26 10:15:45', status: 'Success' },
  { audit_id: 'AUD005', user: 'Eve Adams', action: 'Login', timestamp: '2023-10-26 10:20:00', status: 'Failed' },
];

const auditFilterOptions = [
  { label: 'Last 7 days', value: 'last7days' },
  { label: 'Last 30 days', value: 'last30days' },
  { label: 'All time', value: 'alltime' },
];

const auditRadioFilters = [
  { label: 'All Audits', value: 'All' },
  { label: 'Successful Audits', value: 'Success' },
  { label: 'Failed Audits', value: 'Failed' },
];

</script>

<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <BreadcrumbsActions :breadcrumbs="auditBreadcrumbs" />
        <DefaultTable
          :columns="auditColumns"
          :data="auditData"
          title="Audits"
          :filterOptions="auditFilterOptions"
          :radioFilters="auditRadioFilters"
          addButtonLabel="Add New Audit Entry"
          :showAddButton="true"
        />
        <Footer />
      </div>
</template>
