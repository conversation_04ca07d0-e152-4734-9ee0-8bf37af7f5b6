<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 19.9998H28V14.6665H0V19.9998Z" fill="#2D754D"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 9.33333H28V0H0V9.33333Z" fill="#25B1EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 14.6668H28V9.3335H0V14.6668Z" fill="#FECB2F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.3334 6L20.3129 7.13035L20.3906 5.60948L18.8697 5.68716L20.0001 4.66667L18.8697 3.64618L20.3906 3.72386L20.3129 2.20299L21.3334 3.33333L22.3539 2.20299L22.2762 3.72386L23.7971 3.64618L22.6667 4.66667L23.7971 5.68716L22.2762 5.60948L22.3539 7.13035L21.3334 6Z" fill="#FECB2F"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="14.6665" width="28" height="5.33333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
