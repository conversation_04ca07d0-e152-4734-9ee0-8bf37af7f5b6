<template>
  <component :is="layoutToUse">
    <h1>Welcome to Sections Management!</h1>
    <p>This is the main content for the sections management page.</p>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import DefaultSectionsLayout from '@/views/admin/user_management/sections/layouts/default-sections-layout.vue';

// Assuming you have a way to get the current tenant, e.g., from a Pinia store or prop
// import { useTenantStore } from '@/store'; // Example tenant store

// const tenantStore = useTenantStore();
// const currentTenant = computed(() => tenantStore.currentTenant);

// For now, we'll use a default layout
const layoutToUse = computed(() => {
  // You can add logic here to determine which layout to use based on tenant or other factors
  return DefaultSectionsLayout;
});
</script>
