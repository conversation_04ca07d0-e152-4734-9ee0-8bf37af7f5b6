import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { sectionService } from '@/services';
import type { 
  SectionDto, 
  CreateSectionRequest, 
  UpdateSectionRequest,
  SectionFilterDto 
} from '@/interfaces';

export const useSectionStore = defineStore('section', () => {
  // State
  const sections = ref<SectionDto[]>([]);
  const currentSection = ref<SectionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<SectionFilterDto>({
    searchQuery: '',
    parentSectionId: 'All',
    level: 'All',
    isActive: 'All',
    hasUsers: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const sectionHierarchy = ref<SectionDto[]>([]);

  // Getters
  const filteredSections = computed(() => {
    let filtered = sections.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(section => 
        section.name?.toLowerCase().includes(query) ||
        section.description?.toLowerCase().includes(query) ||
        section.code?.toLowerCase().includes(query)
      );
    }

    if (filters.value.parentSectionId !== 'All') {
      filtered = filtered.filter(section => section.parentSectionId === filters.value.parentSectionId);
    }

    if (filters.value.level !== 'All') {
      filtered = filtered.filter(section => section.level === filters.value.level);
    }

    if (filters.value.isActive !== 'All') {
      filtered = filtered.filter(section => section.isActive === filters.value.isActive);
    }

    if (filters.value.hasUsers !== 'All') {
      filtered = filtered.filter(section => {
        const hasUsers = section.userCount && section.userCount > 0;
        return filters.value.hasUsers ? hasUsers : !hasUsers;
      });
    }

    return filtered;
  });

  const sectionsByLevel = computed(() => {
    const grouped: Record<number, SectionDto[]> = {};
    sections.value.forEach(section => {
      if (!grouped[section.level]) {
        grouped[section.level] = [];
      }
      grouped[section.level].push(section);
    });
    return grouped;
  });

  const rootSections = computed(() => {
    return sections.value.filter(section => section.level === 0);
  });

  const sectionsByStatus = computed(() => {
    return {
      active: sections.value.filter(s => s.isActive).length,
      inactive: sections.value.filter(s => !s.isActive).length,
      withUsers: sections.value.filter(s => s.userCount && s.userCount > 0).length,
      withoutUsers: sections.value.filter(s => !s.userCount || s.userCount === 0).length,
    };
  });

  const totalSections = computed(() => sections.value.length);
  const activeSections = computed(() => sections.value.filter(s => s.isActive).length);

  // Actions
  const fetchSections = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      sections.value = await sectionService.getAllSections();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionsPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await sectionService.getSectionsPaginated(page, limit);
      sections.value = response.sections;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionById = async (id: string): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const section = await sectionService.getSectionById(id);
      currentSection.value = section;
      return section;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createSection = async (sectionData: CreateSectionRequest): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newSection = await sectionService.createSection(sectionData);
      sections.value.push(newSection);
      return newSection;
    } catch (err: any) {
      error.value = err.message || 'Failed to create section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSection = async (id: string, sectionData: UpdateSectionRequest): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedSection = await sectionService.updateSection(id, sectionData);
      
      const index = sections.value.findIndex(s => s.id === id);
      if (index !== -1) {
        sections.value[index] = updatedSection;
      }
      
      if (currentSection.value?.id === id) {
        currentSection.value = updatedSection;
      }
      
      return updatedSection;
    } catch (err: any) {
      error.value = err.message || 'Failed to update section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteSection = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.deleteSection(id);
      
      sections.value = sections.value.filter(s => s.id !== id);
      
      if (currentSection.value?.id === id) {
        currentSection.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionHierarchy = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      sectionHierarchy.value = await sectionService.getSectionHierarchy();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section hierarchy';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRootSections = async (): Promise<SectionDto[]> => {
    try {
      return await sectionService.getRootSections();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch root sections';
      throw err;
    }
  };

  const fetchChildSections = async (parentId: string): Promise<SectionDto[]> => {
    try {
      return await sectionService.getChildSections(parentId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch child sections';
      throw err;
    }
  };

  const fetchSectionPath = async (sectionId: string): Promise<SectionDto[]> => {
    try {
      return await sectionService.getSectionPath(sectionId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section path';
      throw err;
    }
  };

  const searchSections = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        sections.value = await sectionService.searchSections(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search sections';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchSections();
    }
  };

  const filterSections = async (filterOptions: SectionFilterDto): Promise<void> => {
    filters.value = { ...filterOptions };
    try {
      isLoading.value = true;
      error.value = null;
      sections.value = await sectionService.filterSections(filterOptions);
    } catch (err: any) {
      error.value = err.message || 'Failed to filter sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const toggleSectionStatus = async (id: string): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedSection = await sectionService.toggleSectionStatus(id);
      
      const index = sections.value.findIndex(s => s.id === id);
      if (index !== -1) {
        sections.value[index] = updatedSection;
      }
      
      if (currentSection.value?.id === id) {
        currentSection.value = updatedSection;
      }
      
      return updatedSection;
    } catch (err: any) {
      error.value = err.message || 'Failed to toggle section status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const moveSection = async (sectionId: string, newParentId: string | null): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedSection = await sectionService.moveSection(sectionId, newParentId);
      
      const index = sections.value.findIndex(s => s.id === sectionId);
      if (index !== -1) {
        sections.value[index] = updatedSection;
      }
      
      if (currentSection.value?.id === sectionId) {
        currentSection.value = updatedSection;
      }
      
      return updatedSection;
    } catch (err: any) {
      error.value = err.message || 'Failed to move section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentSection = (): void => {
    currentSection.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      parentSectionId: 'All',
      level: 'All',
      isActive: 'All',
      hasUsers: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    sections,
    currentSection,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    sectionHierarchy,
    
    // Getters
    filteredSections,
    sectionsByLevel,
    rootSections,
    sectionsByStatus,
    totalSections,
    activeSections,
    
    // Actions
    fetchSections,
    fetchSectionsPaginated,
    fetchSectionById,
    createSection,
    updateSection,
    deleteSection,
    fetchSectionHierarchy,
    fetchRootSections,
    fetchChildSections,
    fetchSectionPath,
    searchSections,
    filterSections,
    toggleSectionStatus,
    moveSection,
    clearError,
    clearCurrentSection,
    resetFilters,
  };
});
